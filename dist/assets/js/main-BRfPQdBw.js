var e=(e,t,i)=>new Promise((n,s)=>{var o=e=>{try{r(i.next(e))}catch(t){s(t)}},a=e=>{try{r(i.throw(e))}catch(t){s(t)}},r=e=>e.done?n(e.value):Promise.resolve(e.value).then(o,a);r((i=i.apply(e,t)).next())});import{N as t,L as i,C as n}from"./components-CRygyYm4.js";import{I as s,V as o}from"./services-DExg8ETr.js";import{r as a,$ as r,p as c,a as l,b as d}from"./utils-2gcXUZnL.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const i of e)if("childList"===i.type)for(const e of i.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const u=new class{constructor(){this.components=new Map,this.services=new Map,this.isInitialized=!1,this.init()}init(){return e(this,null,function*(){try{yield this.waitForDOM(),yield this.setupServices(),this.initializeComponents(),this.setupGlobalEventListeners(),this.setupAccessibility(),this.setupAnimations(),this.isInitialized=!0,this.announceReady()}catch(e){}})}waitForDOM(){return new Promise(e=>{a(e)})}setupServices(){return e(this,null,function*(){const e=new s({defaultLanguage:"nl",fallbackLanguage:"nl"});yield e.init(),this.services.set("i18n",e);const t=new o(e.getCurrentLanguage());this.services.set("validation",t)})}initializeComponents(){this.initNavigation(),this.initLanguageSwitcher(),this.initContactForm(),this.initScrollEffects(),this.initFAQ()}initNavigation(){const e=r(".header nav");if(e){const i=new t(e,{mobileBreakpoint:768,closeOnOutsideClick:!0,closeOnEscape:!0});this.components.set("navigation",i),this.setActiveNavigation()}}initLanguageSwitcher(){const e=r(".language-switcher");if(e){const t=this.services.get("i18n"),n=new i(e,t);this.components.set("languageSwitcher",n)}}initContactForm(){const e=r("#contactForm");if(e){const t=new n(e,{validateOnBlur:!0,showSuccessMessage:!0,resetOnSuccess:!0,trackAnalytics:!1});this.components.set("contactForm",t)}}initScrollEffects(){this.setupSmoothScrolling(),c()||this.setupScrollAnimations(),this.setupHeaderScrollEffect()}setupSmoothScrolling(){l('a[href^="#"]').forEach(e=>{e.addEventListener("click",t=>{const i=e.getAttribute("href");if("#"===i)return;const n=r(i);n&&(t.preventDefault(),n.scrollIntoView({behavior:"smooth",block:"start"}))})})}setupScrollAnimations(){const e=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(t.target.classList.add("animate-in"),e.unobserve(t.target))})},{threshold:.1,rootMargin:"0px 0px -50px 0px"});l(".fade-in-section, .service-card, .card").forEach(t=>{e.observe(t)})}setupHeaderScrollEffect(){const e=r(".header");if(!e)return;let t=!1;const i=()=>{window.scrollY>100?e.classList.add("scrolled"):e.classList.remove("scrolled"),t=!1};window.addEventListener("scroll",()=>{t||(requestAnimationFrame(i),t=!0)})}initFAQ(){const e=l(".faq-item");e.forEach(t=>{const i=t.querySelector(".faq-question"),n=t.querySelector(".faq-answer");i&&n&&(i.addEventListener("click",()=>{const s=i.classList.contains("active");e.forEach(e=>{const i=e.querySelector(".faq-question"),n=e.querySelector(".faq-answer");e!==t&&(i.classList.remove("active"),n.classList.remove("active"),i.setAttribute("aria-expanded","false"))}),s?(i.classList.remove("active"),n.classList.remove("active"),i.setAttribute("aria-expanded","false")):(i.classList.add("active"),n.classList.add("active"),i.setAttribute("aria-expanded","true"))}),i.setAttribute("aria-expanded","false"),i.setAttribute("role","button"),i.setAttribute("tabindex","0"),i.addEventListener("keydown",e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),i.click())}))})}setupGlobalEventListeners(){window.addEventListener("resize",this.handleResize.bind(this)),document.addEventListener("visibilitychange",this.handleVisibilityChange.bind(this)),window.addEventListener("error",this.handleError.bind(this))}setupAccessibility(){this.setupSkipLinks(),this.setupFocusManagement(),this.setupKeyboardNavigation()}setupSkipLinks(){l(".skip-link").forEach(e=>{e.addEventListener("click",t=>{const i=e.getAttribute("href"),n=r(i);n&&(t.preventDefault(),n.setAttribute("tabindex","-1"),n.focus(),n.scrollIntoView({behavior:"smooth"}))})})}setupFocusManagement(){document.addEventListener("keydown",e=>{"Tab"===e.key&&document.body.classList.add("keyboard-navigation")}),document.addEventListener("mousedown",()=>{document.body.classList.remove("keyboard-navigation")})}setupKeyboardNavigation(){document.addEventListener("keydown",e=>{if(e.altKey&&"m"===e.key){e.preventDefault();const t=r(".nav");if(t){const e=t.querySelector(".nav-link");e&&e.focus()}}if(e.altKey&&"c"===e.key){e.preventDefault();const t=r("#main-content");t&&(t.setAttribute("tabindex","-1"),t.focus())}})}setupAnimations(){const e=document.createElement("style");e.textContent="\n      .animate-in {\n        animation: fadeInUp 0.6s ease-out forwards;\n      }\n      \n      @keyframes fadeInUp {\n        from {\n          opacity: 0;\n          transform: translateY(30px);\n        }\n        to {\n          opacity: 1;\n          transform: translateY(0);\n        }\n      }\n      \n      .keyboard-navigation *:focus {\n        outline: 2px solid #0056b3 !important;\n        outline-offset: 2px !important;\n      }\n    ",document.head.appendChild(e)}setActiveNavigation(){const e=this.components.get("navigation");if(e){const t=window.location.pathname;e.setActive(t)}}handleResize(){clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.components.forEach(e=>{"function"==typeof e.handleResize&&e.handleResize()})},250)}handleVisibilityChange(){document.hidden}handleError(e){d("Er is een fout opgetreden. Probeer de pagina te vernieuwen.","assertive")}announceReady(){d("Website geladen en klaar voor gebruik","polite")}getComponent(e){return this.components.get(e)}getService(e){return this.services.get(e)}destroy(){this.components.forEach(e=>{"function"==typeof e.destroy&&e.destroy()}),this.components.clear(),this.services.clear(),this.isInitialized=!1}};"undefined"!=typeof window&&(window.TechSupportApp=u);
