var e=Object.defineProperty,t=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,i=(t,a,n)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[a]=n,r=(e,r)=>{for(var s in r||(r={}))a.call(r,s)&&i(e,s,r[s]);if(t)for(var s of t(r))n.call(r,s)&&i(e,s,r[s]);return e},s=(e,t,a)=>new Promise((n,i)=>{var r=e=>{try{l(a.next(e))}catch(t){i(t)}},s=e=>{try{l(a.throw(e))}catch(t){i(t)}},l=e=>e.done?n(e.value):Promise.resolve(e.value).then(r,s);l((a=a.apply(e,t)).next())});class l{constructor(e="nl"){this.locale=e,this.messages={nl:{required:"Dit veld is verplicht",email:"Vul een geldig e-mailadres in",minLength:"Minimaal {min} karakters vereist",maxLength:"Maximaal {max} karakters toegestaan",pattern:"Ongeldige invoer",phone:"Vul een geldig telefoonnummer in",url:"Vul een geldige URL in",number:"Vul een geldig nummer in",min:"Waarde moet minimaal {min} zijn",max:"Waarde mag maximaal {max} zijn",match:"Velden komen niet overeen",custom:"Ongeldige invoer"},en:{required:"This field is required",email:"Please enter a valid email address",minLength:"Minimum {min} characters required",maxLength:"Maximum {max} characters allowed",pattern:"Invalid input",phone:"Please enter a valid phone number",url:"Please enter a valid URL",number:"Please enter a valid number",min:"Value must be at least {min}",max:"Value must be at most {max}",match:"Fields do not match",custom:"Invalid input"}},this.rules={required:this.validateRequired.bind(this),email:this.validateEmail.bind(this),phone:this.validatePhone.bind(this),url:this.validateUrl.bind(this),number:this.validateNumber.bind(this),minLength:this.validateMinLength.bind(this),maxLength:this.validateMaxLength.bind(this),min:this.validateMin.bind(this),max:this.validateMax.bind(this),pattern:this.validatePattern.bind(this),match:this.validateMatch.bind(this),custom:this.validateCustom.bind(this)}}validate(e,t=[],a={}){const n=[];for(const i of t){const[t,...r]=i.split(":"),s=this.rules[t];if(!s)continue;if(!s(e,r,a)){const e=this.getErrorMessage(t,r);n.push(e);break}}return n}validateRequired(e){return"boolean"==typeof e?!0===e:"string"==typeof e?e.trim().length>0:Array.isArray(e)?e.length>0:null!=e&&""!==e}validateEmail(e){if(!e)return!0;return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}validatePhone(e){if(!e)return!0;const t=e.replace(/[\s\-\(\)]/g,"");return/^(\+31|0031|0)[1-9][0-9]{8}$|^(\+31|0031|0)[1-9][0-9]{7}$/.test(t)}validateUrl(e){if(!e)return!0;try{return new URL(e),!0}catch(t){return!1}}validateNumber(e){return!e||!isNaN(e)&&!isNaN(parseFloat(e))}validateMinLength(e,t){if(!e)return!0;const a=parseInt(t[0],10);return e.toString().length>=a}validateMaxLength(e,t){if(!e)return!0;const a=parseInt(t[0],10);return e.toString().length<=a}validateMin(e,t){if(!e)return!0;const a=parseFloat(t[0]),n=parseFloat(e);return!isNaN(n)&&n>=a}validateMax(e,t){if(!e)return!0;const a=parseFloat(t[0]),n=parseFloat(e);return!isNaN(n)&&n<=a}validatePattern(e,t){if(!e)return!0;return new RegExp(t[0]).test(e)}validateMatch(e,t,a){if(!e)return!0;return e===a[t[0]]}validateCustom(e,t,a){var n;const i=null==(n=a.customValidators)?void 0:n[t[0]];return"function"!=typeof i||i(e,a)}getErrorMessage(e,t=[]){const a=this.messages[this.locale]||this.messages.nl;let n=a[e]||a.custom;return t.forEach((e,t)=>{const a=0===t?"{min}":1===t?"{max}":`{${t}}`;n=n.replace(a,e)}),n}addRule(e,t,a){this.rules[e]=t,a&&Object.keys(this.messages).forEach(t=>{this.messages[t][e]=a[t]||a})}setLocale(e){this.locale=e}validateForm(e,t){const a={};let n=!0;return Object.entries(t).forEach(([t,i])=>{const r=e[t],s=this.validate(r,i,e);s.length>0&&(a[t]=s,n=!1)}),{isValid:n,errors:a}}createRealtimeValidator(e,t,a={}){const{debounceTime:n=300,validateOnBlur:i=!0,validateOnInput:r=!0,errorElement:s=null,onValid:l=null,onInvalid:o=null}=a;let u;const g=()=>{const a="checkbox"===e.type?e.checked:e.value,n=this.validate(a,t),i=0===n.length;return e.setAttribute("aria-invalid",!i),s&&(s.textContent=n[0]||"",s.style.display=n.length>0?"block":"none"),i&&l?l(e,a):!i&&o&&o(e,n),i},c=()=>{clearTimeout(u),u=setTimeout(g,n)};return r&&e.addEventListener("input",c),i&&e.addEventListener("blur",g),()=>{clearTimeout(u),e.removeEventListener("input",c),e.removeEventListener("blur",g)}}}class o{constructor(e={}){this.options=r({apiEndpoint:"/api/contact",timeout:1e4},e)}submitForm(e){return s(this,null,function*(){try{if(yield this.delay(1e3),!e.name||!e.email||!e.message)throw new Error("Alle verplichte velden moeten worden ingevuld");return{success:!0,message:"Uw bericht is succesvol verzonden!",referenceNumber:this.generateReferenceNumber(),data:e}}catch(t){return{success:!1,message:t.message||"Er is een fout opgetreden bij het verzenden van uw bericht",error:t}}})}generateReferenceNumber(){return`TSP-${Date.now().toString().slice(-6)}-${Math.random().toString(36).substring(2,5).toUpperCase()}`}delay(e){return new Promise(t=>setTimeout(t,e))}isValidEmail(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}sanitizeFormData(e){const t={};return Object.entries(e).forEach(([e,a])=>{t[e]="string"==typeof a?a.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/<[^>]*>/g,""):a}),t}}class u{constructor(e={}){this.options=r({defaultLanguage:"nl",fallbackLanguage:"nl",storageKey:"techsupport_language"},e),this.currentLanguage=this.options.defaultLanguage,this.translations=new Map,this.isLoading=!1,this.init()}init(){return s(this,null,function*(){this.currentLanguage=this.detectLanguage(),yield this.loadLanguage(this.currentLanguage),this.applyTranslations(),document.documentElement.lang=this.currentLanguage,this.saveLanguagePreference()})}detectLanguage(){const e=new URLSearchParams(window.location.search).get("lang");if(e&&this.isValidLanguage(e))return e;const t=localStorage.getItem(this.options.storageKey);if(t&&this.isValidLanguage(t))return t;const a=navigator.language.split("-")[0];return this.isValidLanguage(a)?a:this.options.defaultLanguage}isValidLanguage(e){return["nl","en","fr"].includes(e)}loadLanguage(e){return s(this,null,function*(){if(this.translations.has(e))return this.translations.get(e);this.isLoading=!0;try{const a=[`/src/data/i18n/${e}.json`,`/data/i18n/${e}.json`,`./src/data/i18n/${e}.json`];let n,i;for(const e of a)try{if(n=yield fetch(e),n.ok)break}catch(t){i=t}if(!n||!n.ok)throw new Error(`Failed to load language file: ${e}`);const r=yield n.json();return this.translations.set(e,r),r}catch(a){if(e!==this.options.fallbackLanguage)return this.loadLanguage(this.options.fallbackLanguage);throw a}finally{this.isLoading=!1}})}switchLanguage(e){return s(this,null,function*(){this.isValidLanguage(e)&&e!==this.currentLanguage&&(yield this.loadLanguage(e),this.currentLanguage=e,this.applyTranslations(),document.documentElement.lang=e,this.saveLanguagePreference(),this.updateURL(),this.updateLanguageSwitcher(),this.triggerLanguageChangeEvent())})}t(e,t={}){const a=this.translations.get(this.currentLanguage);if(!a)return e;const n=this.getNestedValue(a,e);if(void 0===n){const a=this.translations.get(this.options.fallbackLanguage);if(a&&this.currentLanguage!==this.options.fallbackLanguage){const n=this.getNestedValue(a,e);if(void 0!==n)return this.interpolate(n,t)}return e}return this.interpolate(n,t)}getNestedValue(e,t){return t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:void 0,e)}interpolate(e,t){return"string"!=typeof e?e:e.replace(/\{\{(\w+)\}\}/g,(e,a)=>void 0!==t[a]?t[a]:e)}applyTranslations(){var e;document.querySelectorAll("[data-i18n]").forEach(e=>{const t=e.getAttribute("data-i18n"),a=this.t(t),n=e.getAttribute("data-i18n-attr");n?e.setAttribute(n,a):e.textContent=a});const t=null==(e=document.querySelector("title"))?void 0:e.getAttribute("data-i18n");t&&(document.title=this.t(t))}saveLanguagePreference(){localStorage.setItem(this.options.storageKey,this.currentLanguage)}updateURL(){const e=new URL(window.location);e.searchParams.set("lang",this.currentLanguage),window.history.replaceState({},"",e.toString())}updateLanguageSwitcher(){const e=document.querySelector(".current-language");if(e){const t={nl:"Nederlands",en:"English",fr:"Français"};e.textContent=t[this.currentLanguage]}document.querySelectorAll("[data-lang]").forEach(e=>{e.getAttribute("data-lang")===this.currentLanguage?(e.classList.add("active"),e.setAttribute("aria-current","true")):(e.classList.remove("active"),e.removeAttribute("aria-current"))})}triggerLanguageChangeEvent(){const e=new CustomEvent("languagechange",{detail:{language:this.currentLanguage,previousLanguage:this.previousLanguage}});document.dispatchEvent(e)}getCurrentLanguage(){return this.currentLanguage}getAvailableLanguages(){return["nl","en","fr"]}isServiceLoading(){return this.isLoading}preloadLanguages(){return s(this,null,function*(){const e=this.getAvailableLanguages().map(e=>this.loadLanguage(e));try{yield Promise.all(e)}catch(t){}})}}export{o as C,u as I,l as V};
