var e=Object.defineProperty,t=Object.defineProperties,i=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,o=(t,i,n)=>i in t?e(t,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[i]=n,a=(e,t)=>{for(var i in t||(t={}))s.call(t,i)&&o(e,i,t[i]);if(n)for(var i of n(t))r.call(t,i)&&o(e,i,t[i]);return e},l=(e,t,i)=>new Promise((n,s)=>{var r=e=>{try{a(i.next(e))}catch(t){s(t)}},o=e=>{try{a(i.throw(e))}catch(t){s(t)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(r,o);a((i=i.apply(e,t)).next())});import{V as c,C as d}from"./services-DExg8ETr.js";class h{constructor(e,t={}){this.nav=e,this.options=a({mobileBreakpoint:768,closeOnOutsideClick:!0,closeOnEscape:!0},t),this.isOpen=!1,this.activeDropdown=null,this.init()}init(){this.nav&&(this.setupElements(),this.bindEvents(),this.setupAccessibility())}setupElements(){this.toggle=this.nav.querySelector(".mobile-menu-toggle"),this.menu=this.nav.querySelector(".nav"),this.overlay=document.querySelector(".nav-overlay")||this.createOverlay(),this.dropdowns=this.nav.querySelectorAll(".nav-dropdown"),this.links=this.nav.querySelectorAll(".nav-link")}createOverlay(){const e=document.createElement("div");return e.className="nav-overlay",e.setAttribute("aria-hidden","true"),document.body.appendChild(e),e}bindEvents(){this.toggle&&this.toggle.addEventListener("click",this.toggleMobile.bind(this)),this.overlay.addEventListener("click",this.closeMobile.bind(this)),this.dropdowns.forEach(e=>{const t=e.querySelector(".nav-link"),i=e.querySelector(".nav-dropdown-menu");t&&i&&t.addEventListener("click",t=>{t.preventDefault(),this.toggleDropdown(e)})}),this.links.forEach(e=>{e.closest(".nav-dropdown")||e.addEventListener("click",()=>{this.isOpen&&this.closeMobile()})}),document.addEventListener("keydown",this.handleKeydown.bind(this)),this.options.closeOnOutsideClick&&document.addEventListener("click",this.handleOutsideClick.bind(this)),window.addEventListener("resize",this.handleResize.bind(this))}setupAccessibility(){this.toggle&&(this.toggle.setAttribute("aria-expanded","false"),this.toggle.setAttribute("aria-controls",this.menu.id||"nav-menu"),this.menu.id||(this.menu.id="nav-menu")),this.dropdowns.forEach((e,t)=>{const i=e.querySelector(".nav-link"),n=e.querySelector(".nav-dropdown-menu");if(i&&n){const e=n.id||`dropdown-menu-${t}`;n.id=e,i.setAttribute("aria-expanded","false"),i.setAttribute("aria-haspopup","true"),i.setAttribute("aria-controls",e),n.setAttribute("role","menu");n.querySelectorAll(".nav-link").forEach(e=>{e.setAttribute("role","menuitem")})}})}toggleMobile(){this.isOpen?this.closeMobile():this.openMobile()}openMobile(){this.isOpen=!0,this.menu.classList.add("active"),this.overlay.classList.add("active"),document.body.style.overflow="hidden",this.toggle&&(this.toggle.setAttribute("aria-expanded","true"),this.toggle.setAttribute("aria-label","Menu sluiten"));const e=this.menu.querySelector(".nav-link");e&&e.focus(),this.announceToScreenReader("Menu geopend")}closeMobile(){this.isOpen=!1,this.menu.classList.remove("active"),this.overlay.classList.remove("active"),document.body.style.overflow="",this.toggle&&(this.toggle.setAttribute("aria-expanded","false"),this.toggle.setAttribute("aria-label","Menu openen")),this.closeAllDropdowns(),this.announceToScreenReader("Menu gesloten")}toggleDropdown(e){e.querySelector(".nav-link");const t=e.querySelector(".nav-dropdown-menu").classList.contains("active");t||this.closeAllDropdowns(),t?this.closeDropdown(e):this.openDropdown(e)}openDropdown(e){const t=e.querySelector(".nav-link"),i=e.querySelector(".nav-dropdown-menu");i.classList.add("active"),t.setAttribute("aria-expanded","true"),this.activeDropdown=e;const n=i.querySelector(".nav-link");n&&n.focus()}closeDropdown(e){const t=e.querySelector(".nav-link");e.querySelector(".nav-dropdown-menu").classList.remove("active"),t.setAttribute("aria-expanded","false"),this.activeDropdown===e&&(this.activeDropdown=null)}closeAllDropdowns(){this.dropdowns.forEach(e=>{this.closeDropdown(e)})}handleKeydown(e){const{key:t}=e;if("Escape"===t&&this.options.closeOnEscape)if(this.activeDropdown){this.closeDropdown(this.activeDropdown);const e=this.activeDropdown.querySelector(".nav-link");e&&e.focus()}else this.isOpen&&(this.closeMobile(),this.toggle&&this.toggle.focus());!this.activeDropdown||"ArrowDown"!==t&&"ArrowUp"!==t||(e.preventDefault(),this.navigateDropdown("ArrowDown"===t?1:-1)),"Tab"===t&&this.isOpen&&this.handleTabNavigation(e)}navigateDropdown(e){const t=this.activeDropdown.querySelector(".nav-dropdown-menu"),i=Array.from(t.querySelectorAll(".nav-link"));let n=i.indexOf(document.activeElement)+e;n<0?n=i.length-1:n>=i.length&&(n=0),i[n].focus()}handleTabNavigation(e){const t=this.menu.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])'),i=t[0],n=t[t.length-1];e.shiftKey?document.activeElement===i&&(e.preventDefault(),n.focus()):document.activeElement===n&&(e.preventDefault(),i.focus())}handleOutsideClick(e){this.nav.contains(e.target)||(this.activeDropdown&&this.closeAllDropdowns(),this.isOpen&&!this.overlay.contains(e.target)&&this.closeMobile())}handleResize(){window.innerWidth>this.options.mobileBreakpoint&&this.isOpen&&this.closeMobile()}announceToScreenReader(e){const t=document.querySelector(".nav-announcer")||this.createAnnouncer();t.textContent=e,setTimeout(()=>{t.textContent=""},1e3)}createAnnouncer(){const e=document.createElement("div");return e.className="nav-announcer",e.setAttribute("aria-live","polite"),e.setAttribute("aria-atomic","true"),document.body.appendChild(e),e}setActive(e){this.links.forEach(t=>{t.classList.remove("active"),t.getAttribute("href")===e?(t.classList.add("active"),t.setAttribute("aria-current","page")):t.removeAttribute("aria-current")})}destroy(){this.toggle&&this.toggle.removeEventListener("click",this.toggleMobile),this.overlay.removeEventListener("click",this.closeMobile),document.removeEventListener("keydown",this.handleKeydown),document.removeEventListener("click",this.handleOutsideClick),window.removeEventListener("resize",this.handleResize),this.closeMobile(),this.closeAllDropdowns(),this.overlay&&this.overlay.parentNode&&this.overlay.parentNode.removeChild(this.overlay)}}class u{constructor(){this.notifications=[],this.container=null,this.announcer=null,this.init()}init(){this.createContainer(),this.createAnnouncer()}createContainer(){this.container=document.createElement("div"),this.container.className="notification-container",this.container.setAttribute("aria-live","polite"),this.container.setAttribute("aria-atomic","false"),this.container.style.cssText="\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      z-index: 9999;\n      max-width: 400px;\n      pointer-events: none;\n    ",document.body.appendChild(this.container)}createAnnouncer(){this.announcer=document.createElement("div"),this.announcer.className="sr-only",this.announcer.setAttribute("aria-live","polite"),this.announcer.setAttribute("aria-atomic","true"),document.body.appendChild(this.announcer)}show(e={}){const t={id:this.generateId(),type:e.type||"info",title:e.title||"",message:e.message||"",duration:e.duration||5e3,persistent:e.persistent||!1,actions:e.actions||[]};return this.notifications.push(t),this.render(t),!t.persistent&&t.duration>0&&setTimeout(()=>{this.remove(t.id)},t.duration),t.id}render(e){const t=document.createElement("div");t.className=`notification notification--${e.type}`,t.setAttribute("role","alert"),t.setAttribute("data-notification-id",e.id),t.style.cssText="\n      background: white;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      padding: 16px;\n      margin-bottom: 12px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      pointer-events: auto;\n      transform: translateX(100%);\n      transition: transform 0.3s ease;\n      max-width: 100%;\n      word-wrap: break-word;\n    ";const i={success:"#28a745",error:"#dc3545",warning:"#ffc107",info:"#17a2b8"};i[e.type]&&(t.style.borderLeftColor=i[e.type],t.style.borderLeftWidth="4px");let n="";e.title&&(n+=`<div style="font-weight: 600; margin-bottom: 8px; color: #333;">${e.title}</div>`),e.message&&(n+=`<div style="color: #666; line-height: 1.4;">${e.message}</div>`),e.actions.length>0&&(n+='<div style="margin-top: 12px; display: flex; gap: 8px;">',e.actions.forEach(t=>{n+=`<button \n          onclick="window.notificationAction('${e.id}', '${t.id}')"\n          style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; font-size: 14px;"\n        >${t.label}</button>`}),n+="</div>"),n+=`\n      <button \n        onclick="window.removeNotification('${e.id}')"\n        style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; cursor: pointer; color: #999; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;"\n        aria-label="Melding sluiten"\n      >×</button>\n    `,t.innerHTML=n,t.style.position="relative",this.container.appendChild(t),setTimeout(()=>{t.style.transform="translateX(0)"},10),this.setupGlobalHandlers()}setupGlobalHandlers(){window.removeNotification||(window.removeNotification=e=>this.remove(e)),window.notificationAction||(window.notificationAction=(e,t)=>{const i=this.notifications.find(t=>t.id===e);if(i){const e=i.actions.find(e=>e.id===t);e&&e.handler&&e.handler()}})}remove(e){const t=this.container.querySelector(`[data-notification-id="${e}"]`);t&&(t.style.transform="translateX(100%)",t.style.opacity="0",setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},300)),this.notifications=this.notifications.filter(t=>t.id!==e)}announce(e,t="polite"){this.announcer&&(this.announcer.setAttribute("aria-live",t),this.announcer.textContent=e,setTimeout(()=>{this.announcer.textContent=""},1e3))}clear(){this.notifications.forEach(e=>{this.remove(e.id)})}generateId(){return`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}success(e,t={}){return this.show(a({type:"success",message:e},t))}error(e,t={}){return this.show(a({type:"error",message:e,duration:0},t))}warning(e,t={}){return this.show(a({type:"warning",message:e},t))}info(e,t={}){return this.show(a({type:"info",message:e},t))}}class m{constructor(e={}){this.options=a({enabled:!1,debug:!1},e),this.events=[]}track(e,t={}){if(!this.options.enabled)return void this.options.debug;const i={name:e,data:t,timestamp:(new Date).toISOString(),url:window.location.href,userAgent:navigator.userAgent};this.events.push(i),this.sendEvent(i),this.options.debug}sendEvent(e){var n,s;"function"==typeof window.gtag&&window.gtag("event",e.name,(n=a({},e.data),s={custom_parameter:e.timestamp},t(n,i(s)))),"function"==typeof window.ga&&window.ga("send","event",{eventCategory:e.data.category||"General",eventAction:e.name,eventLabel:e.data.label,eventValue:e.data.value}),this.options.endpoint&&this.sendToEndpoint(e)}sendToEndpoint(e){return l(this,null,function*(){try{yield fetch(this.options.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(t){}})}pageView(e=window.location.pathname,t=document.title){this.track("page_view",{page:e,title:t,referrer:document.referrer})}interaction(e,t,i={}){this.track("user_interaction",a({element:e,action:t},i))}formSubmission(e,t,i={}){this.track("form_submission",a({form_name:e,success:t},i))}error(e,t,i={}){this.track("error",a({error_message:e,error_context:t},i))}performance(e,t,i={}){this.track("performance",a({metric_name:e,metric_value:t},i))}enable(){this.options.enabled=!0}disable(){this.options.enabled=!1}isEnabled(){return this.options.enabled}getEvents(){return[...this.events]}clearEvents(){this.events=[]}}class p{constructor(e,t={}){this.form=e,this.options=a({validateOnBlur:!0,showSuccessMessage:!0,resetOnSuccess:!0,trackAnalytics:!0},t),this.fields={},this.validators=new c,this.contactService=new d,this.notifications=new u,this.analytics=new m,this.init()}init(){this.form&&(this.setupFields(),this.bindEvents(),this.setupAccessibility(),this.options.trackAnalytics&&this.analytics.track("contact_form_loaded"))}setupFields(){this.fields={name:{element:this.form.querySelector("#name"),errorElement:this.form.querySelector("#name-error"),rules:["required","minLength:2","maxLength:50"]},email:{element:this.form.querySelector("#email"),errorElement:this.form.querySelector("#email-error"),rules:["required","email"]},subject:{element:this.form.querySelector("#subject"),errorElement:this.form.querySelector("#subject-error"),rules:["required"]},message:{element:this.form.querySelector("#message"),errorElement:this.form.querySelector("#message-error"),rules:["required","minLength:10","maxLength:1000"]},terms:{element:this.form.querySelector("#terms"),errorElement:this.form.querySelector("#terms-error"),rules:["required"]}}}bindEvents(){this.form.addEventListener("submit",this.handleSubmit.bind(this));const e=this.form.querySelector('button[type="reset"]');e&&e.addEventListener("click",this.handleReset.bind(this)),this.options.validateOnBlur&&Object.values(this.fields).forEach(e=>{e.element&&e.element.addEventListener("blur",()=>{this.validateField(e)})}),this.fields.email.element&&this.fields.email.element.addEventListener("input",this.debounce(()=>this.validateField(this.fields.email),500))}setupAccessibility(){Object.entries(this.fields).forEach(([e,t])=>{t.element&&t.errorElement&&(t.element.setAttribute("aria-describedby",t.errorElement.id),t.errorElement.setAttribute("aria-live","polite"))}),this.form.setAttribute("role","form"),this.form.setAttribute("aria-label","Contact formulier")}handleSubmit(e){return l(this,null,function*(){e.preventDefault(),this.options.trackAnalytics&&this.analytics.track("contact_form_submit_attempted");if(!this.validateForm())return this.focusFirstError(),void this.announceErrors();this.setLoadingState(!0);try{const e=this.collectFormData(),t=yield this.contactService.submitForm(e);if(!t.success)throw new Error(t.message||"Submission failed");this.handleSuccess(t),this.options.trackAnalytics&&this.analytics.track("contact_form_submit_success")}catch(t){this.handleError(t),this.options.trackAnalytics&&this.analytics.track("contact_form_submit_error",{error:t.message})}finally{this.setLoadingState(!1)}})}validateForm(){let e=!0;return Object.values(this.fields).forEach(t=>{this.validateField(t)||(e=!1)}),e}validateField(e){if(!e.element)return!0;const t="checkbox"===e.element.type?e.element.checked:e.element.value.trim(),i=this.validators.validate(t,e.rules);return i.length>0?(this.showFieldError(e,i[0]),!1):(this.clearFieldError(e),!0)}showFieldError(e,t){e.errorElement&&(e.errorElement.textContent=t,e.errorElement.classList.add("visible")),e.element&&(e.element.setAttribute("aria-invalid","true"),e.element.classList.add("error"))}clearFieldError(e){e.errorElement&&(e.errorElement.textContent="",e.errorElement.classList.remove("visible")),e.element&&(e.element.setAttribute("aria-invalid","false"),e.element.classList.remove("error"))}focusFirstError(){const e=Object.values(this.fields).find(e=>e.element&&"true"===e.element.getAttribute("aria-invalid"));e&&e.element.focus()}announceErrors(){const e=Object.values(this.fields).filter(e=>e.element&&"true"===e.element.getAttribute("aria-invalid")).length;this.notifications.announce(`Er ${1===e?"is":"zijn"} ${e} fout${1===e?"":"en"} gevonden in het formulier.`,"assertive")}collectFormData(){const e={};return Object.entries(this.fields).forEach(([t,i])=>{i.element&&(e[t]="checkbox"===i.element.type?i.element.checked:i.element.value.trim())}),e}handleSuccess(e){this.options.showSuccessMessage&&this.showSuccessMessage(e),this.options.resetOnSuccess&&(this.form.reset(),this.clearAllErrors()),this.notifications.announce("Uw bericht is succesvol verzonden!","polite")}handleError(e){this.notifications.show({type:"error",title:"Fout bij verzenden",message:"Er is een fout opgetreden bij het verzenden van uw bericht. Probeer het later opnieuw.",duration:5e3})}showSuccessMessage(e){const t=`\n      <div class="form-success" role="alert" aria-live="polite">\n        <div class="success-icon">\n          <svg viewBox="0 0 24 24" width="48" height="48">\n            <circle cx="12" cy="12" r="11" fill="#28a745" stroke="white" stroke-width="2"></circle>\n            <path fill="white" d="M9.7 15.3l-3.3-3.3-1.4 1.4 4.7 4.7 10-10-1.4-1.4z"></path>\n          </svg>\n        </div>\n        <h3>Bedankt voor uw bericht!</h3>\n        <p>We hebben uw aanvraag ontvangen en nemen zo spoedig mogelijk contact met u op.</p>\n        <p><strong>Referentienummer:</strong> ${e.referenceNumber||this.generateReferenceNumber()}</p>\n        <button type="button" class="btn btn--secondary" id="newFormBtn">Nieuw bericht</button>\n      </div>\n    `;this.form.style.display="none",this.form.insertAdjacentHTML("afterend",t);const i=document.getElementById("newFormBtn");i&&i.addEventListener("click",()=>{this.resetToForm()})}resetToForm(){const e=this.form.parentNode.querySelector(".form-success");e&&e.remove(),this.form.style.display="block",this.form.reset(),this.clearAllErrors();const t=Object.values(this.fields).find(e=>e.element);t&&t.element.focus()}setLoadingState(e){const t=this.form.querySelector('button[type="submit"]');t&&(e?(t.classList.add("btn--loading"),t.disabled=!0,t.setAttribute("aria-label","Bezig met verzenden...")):(t.classList.remove("btn--loading"),t.disabled=!1,t.setAttribute("aria-label","Bericht verzenden")))}clearAllErrors(){Object.values(this.fields).forEach(e=>{this.clearFieldError(e)})}handleReset(e){confirm("Weet u zeker dat u het formulier wilt leegmaken?")?(this.clearAllErrors(),this.options.trackAnalytics&&this.analytics.track("contact_form_reset")):e.preventDefault()}generateReferenceNumber(){return`TSP-${Date.now().toString().slice(-6)}`}debounce(e,t){let i;return function(...n){clearTimeout(i),i=setTimeout(()=>{clearTimeout(i),e(...n)},t)}}destroy(){this.form.removeEventListener("submit",this.handleSubmit),Object.values(this.fields).forEach(e=>{e.element&&(e.element.removeEventListener("blur",this.validateField),e.element.removeEventListener("input",this.validateField))})}}class v{constructor(e,t,i={}){this.element=e,this.i18nService=t,this.options=a({showFlags:!0,showText:!0},i),this.isOpen=!1,this.init()}init(){this.element&&this.i18nService&&(this.setupElements(),this.bindEvents(),this.updateUI())}setupElements(){this.toggle=this.element.querySelector(".nav-link"),this.menu=this.element.querySelector(".nav-dropdown-menu"),this.currentLangElement=this.element.querySelector(".current-language"),this.langLinks=this.element.querySelectorAll("[data-lang]")}bindEvents(){this.toggle&&this.toggle.addEventListener("click",e=>{e.preventDefault(),this.toggleDropdown()}),this.langLinks.forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const i=e.getAttribute("data-lang");this.switchLanguage(i)})}),document.addEventListener("click",e=>{this.element.contains(e.target)||this.closeDropdown()}),this.element.addEventListener("keydown",this.handleKeydown.bind(this)),document.addEventListener("languagechange",this.handleLanguageChange.bind(this))}toggleDropdown(){this.isOpen?this.closeDropdown():this.openDropdown()}openDropdown(){this.isOpen=!0,this.menu.classList.add("active"),this.toggle.setAttribute("aria-expanded","true");const e=this.menu.querySelector(".nav-link");e&&e.focus()}closeDropdown(){this.isOpen=!1,this.menu.classList.remove("active"),this.toggle.setAttribute("aria-expanded","false")}switchLanguage(e){return l(this,null,function*(){if(!this.i18nService.isServiceLoading())try{this.setLoadingState(!0),yield this.i18nService.switchLanguage(e),this.closeDropdown(),this.updateUI()}catch(t){this.showError("Failed to switch language. Please try again.")}finally{this.setLoadingState(!1)}})}handleKeydown(e){const{key:t}=e;if("Escape"===t)return this.closeDropdown(),void this.toggle.focus();if(!this.isOpen)return;const i=Array.from(this.langLinks),n=i.indexOf(document.activeElement);if("ArrowDown"===t){e.preventDefault();i[n<i.length-1?n+1:0].focus()}else if("ArrowUp"===t){e.preventDefault();i[n>0?n-1:i.length-1].focus()}else if(("Enter"===t||" "===t)&&(e.preventDefault(),document.activeElement.hasAttribute("data-lang"))){const e=document.activeElement.getAttribute("data-lang");this.switchLanguage(e)}}handleLanguageChange(e){this.updateUI()}updateUI(){const e=this.i18nService.getCurrentLanguage();if(this.currentLangElement){const t={nl:"Nederlands",en:"English",fr:"Français"};this.currentLangElement.textContent=t[e]}this.langLinks.forEach(t=>{t.getAttribute("data-lang")===e?(t.classList.add("active"),t.setAttribute("aria-current","true")):(t.classList.remove("active"),t.removeAttribute("aria-current"))}),this.options.showFlags&&this.updateFlags()}updateFlags(){const e=this.i18nService.getCurrentLanguage(),t=this.toggle.querySelector(".flag-icon");t&&(t.src=`/src/assets/images/flags/${e}.svg`,t.alt=`${e.toUpperCase()} flag`)}setLoadingState(e){e?(this.element.classList.add("loading"),this.toggle.setAttribute("aria-busy","true")):(this.element.classList.remove("loading"),this.toggle.setAttribute("aria-busy","false"))}showError(e){const t=document.createElement("div");t.className="language-error",t.textContent=e,t.setAttribute("role","alert"),t.style.cssText="\n      position: absolute;\n      top: 100%;\n      left: 0;\n      right: 0;\n      background: #dc3545;\n      color: white;\n      padding: 8px 12px;\n      border-radius: 4px;\n      font-size: 14px;\n      z-index: 1000;\n      margin-top: 4px;\n    ",this.element.style.position="relative",this.element.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)}getCurrentLanguage(){return this.i18nService.getCurrentLanguage()}destroy(){document.removeEventListener("click",this.handleOutsideClick),document.removeEventListener("languagechange",this.handleLanguageChange),this.langLinks.forEach(e=>{e.removeEventListener("click",this.handleLanguageClick)}),this.toggle&&this.toggle.removeEventListener("click",this.handleToggleClick)}}export{p as C,v as L,h as N};
