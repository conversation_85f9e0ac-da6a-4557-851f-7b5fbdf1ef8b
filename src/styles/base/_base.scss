// =============================================================================
// BASE STYLES
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Document & Body
// -----------------------------------------------------------------------------

html {
  scroll-behavior: smooth;
  
  @media (prefers-reduced-motion: reduce) {
    scroll-behavior: auto;
  }
}

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-primary;
  
  // Remove focus outline for mouse users
  &:not(.keyboard-navigation) *:focus {
    outline: none;
  }
}

// -----------------------------------------------------------------------------
// Main Content
// -----------------------------------------------------------------------------

main {
  flex: 1;
}

// -----------------------------------------------------------------------------
// Selection
// -----------------------------------------------------------------------------

::selection {
  background-color: rgba($primary-color, 0.2);
  color: $text-primary;
}

::-moz-selection {
  background-color: rgba($primary-color, 0.2);
  color: $text-primary;
}

// -----------------------------------------------------------------------------
// Scrollbar Styling
// -----------------------------------------------------------------------------

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: $bg-secondary;
}

::-webkit-scrollbar-thumb {
  background-color: $border-color;
  border-radius: 4px;
  
  &:hover {
    background-color: $text-muted;
  }
}

// -----------------------------------------------------------------------------
// Accessibility Utilities
// -----------------------------------------------------------------------------

.visually-hidden {
  @include visually-hidden;
}

.sr-only {
  @include visually-hidden;
}

// Focus management for better accessibility
.focus-trap {
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
}

// -----------------------------------------------------------------------------
// Reduced Motion
// -----------------------------------------------------------------------------

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  // Disable transform animations for reduced motion
  .fade-in-section,
  .slide-in-left,
  .slide-in-right,
  .scale-in,
  .hover-lift,
  .hover-scale {
    transform: none !important;
  }
}
