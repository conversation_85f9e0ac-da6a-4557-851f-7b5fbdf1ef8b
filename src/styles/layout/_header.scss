// =============================================================================
// HEADER
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Enhanced Professional Header
// -----------------------------------------------------------------------------

.header {
  position: sticky;
  top: 0;
  z-index: $z-index-sticky;
  background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
  box-shadow: $shadow-sm;
  transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba($text-light, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.05;
    pointer-events: none;
  }

  &.scrolled {
    box-shadow: $shadow-lg;
    background: linear-gradient(135deg, $primary-600 0%, $primary-700 100%);

    .header-container {
      padding-top: map-get($spacers, 2);
      padding-bottom: map-get($spacers, 2);
    }

    .logo img {
      height: 36px;

      @include media-breakpoint-down(sm) {
        height: 28px;
      }
    }
  }
}

.header-container {
  @include container;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: $header-height;
  padding-top: map-get($spacers, 4);
  padding-bottom: map-get($spacers, 4);
  position: relative;
  z-index: 1;
  transition: padding $animation-duration-base ease;
}

// -----------------------------------------------------------------------------
// Enhanced Logo
// -----------------------------------------------------------------------------

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: $text-light;
  font-weight: $font-weight-bold;
  font-size: $font-size-lg;
  transition: all $animation-duration-base ease;
  border-radius: $border-radius;
  padding: map-get($spacers, 2);
  margin: calc(-1 * #{map-get($spacers, 2)});

  &:hover,
  &:focus {
    color: $text-light;
    text-decoration: none;
    background-color: rgba($text-light, 0.1);
    transform: scale(1.02);
  }

  &:focus-visible {
    outline: 2px solid $accent-color;
    outline-offset: 2px;
  }

  img {
    height: 44px;
    width: auto;
    transition: all $animation-duration-base ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));

    @include media-breakpoint-down(sm) {
      height: 36px;
    }
  }
}

// -----------------------------------------------------------------------------
// Enhanced Skip Links (Accessibility)
// -----------------------------------------------------------------------------

.skip-links {
  position: absolute;
  top: -200px;
  left: map-get($spacers, 4);
  z-index: $z-index-modal;
  display: flex;
  gap: map-get($spacers, 2);

  .skip-link {
    position: relative;
    top: 0;
    padding: map-get($spacers, 3) map-get($spacers, 5);
    background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
    color: $text-light;
    text-decoration: none;
    font-weight: $font-weight-semibold;
    font-size: $font-size-sm;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-lg;
    transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
    border: 2px solid $accent-color;
    letter-spacing: $letter-spacing-wide;

    &:focus {
      transform: translateY(220px);
      background: linear-gradient(135deg, $accent-color 0%, $accent-dark 100%);
      color: $text-primary;
    }

    &:hover {
      background: linear-gradient(135deg, $primary-600 0%, $primary-700 100%);
      transform: translateY(2px);
    }
  }
}

// -----------------------------------------------------------------------------
// Mobile Menu Toggle
// -----------------------------------------------------------------------------

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  color: $text-light;
  background: none;
  border: 2px solid transparent;
  border-radius: $border-radius-lg;
  transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
  cursor: pointer;
  position: relative;

  &:hover,
  &:focus {
    background-color: rgba($text-light, 0.1);
    border-color: rgba($accent-color, 0.3);
    transform: scale(1.05);
  }

  &:focus-visible {
    outline: 2px solid $accent-color;
    outline-offset: 2px;
  }

  &:active {
    transform: scale(0.95);
  }

  @include media-breakpoint-down(md) {
    display: flex;
  }

  .hamburger-line {
    width: 22px;
    height: 3px;
    background-color: currentColor;
    border-radius: 2px;
    transition: all $animation-duration-base $animation-curve-fast-out-slow-in;

    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }

  &[aria-expanded='true'] {
    background-color: rgba($accent-color, 0.1);
    border-color: $accent-color;

    .hamburger-line {
      &:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
        background-color: $accent-color;
      }

      &:nth-child(2) {
        opacity: 0;
        transform: scale(0);
      }

      &:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
        background-color: $accent-color;
      }
    }
  }
}

// -----------------------------------------------------------------------------
// Enhanced Responsive Behavior
// -----------------------------------------------------------------------------

@include media-breakpoint-down(md) {
  .header {
    &.scrolled {
      .mobile-menu-toggle {
        width: 44px;
        height: 44px;

        .hamburger-line {
          width: 20px;
          height: 2px;
        }
      }
    }
  }
}

@include media-breakpoint-down(sm) {
  .header-container {
    padding-top: map-get($spacers, 3);
    padding-bottom: map-get($spacers, 3);
  }

  .header.scrolled .header-container {
    padding-top: map-get($spacers, 2);
    padding-bottom: map-get($spacers, 2);
  }

  .logo {
    font-size: $font-size-base;

    img {
      height: 36px;
    }
  }

  .mobile-menu-toggle {
    width: 44px;
    height: 44px;

    .hamburger-line {
      width: 20px;
      height: 2px;
    }
  }
}

// Header scroll animation enhancement
.header {
  &.scrolled {
    animation: headerSlideDown 0.3s ease-out;
  }
}

@keyframes headerSlideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
