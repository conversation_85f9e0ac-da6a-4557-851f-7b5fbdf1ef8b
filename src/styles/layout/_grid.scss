// =============================================================================
// GRID SYSTEM
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Enhanced Container System
// -----------------------------------------------------------------------------

.container {
  @include container;

  // Enhanced padding for better visual rhythm
  padding-left: map-get($spacers, 4);
  padding-right: map-get($spacers, 4);

  @include media-breakpoint-up(sm) {
    padding-left: map-get($spacers, 6);
    padding-right: map-get($spacers, 6);
  }

  @include media-breakpoint-up(lg) {
    padding-left: map-get($spacers, 8);
    padding-right: map-get($spacers, 8);
  }
}

.container-fluid {
  width: 100%;
  padding: 0 map-get($spacers, 4);

  @include media-breakpoint-up(sm) {
    padding: 0 map-get($spacers, 6);
  }

  @include media-breakpoint-up(lg) {
    padding: 0 map-get($spacers, 8);
  }
}

// Content-specific containers for better readability
.container-narrow {
  max-width: map-get($content-max-widths, hero);
  margin: 0 auto;
  padding: 0 map-get($spacers, 4);

  @include media-breakpoint-up(sm) {
    padding: 0 map-get($spacers, 6);
  }
}

.container-text {
  max-width: map-get($content-max-widths, prose);
  margin: 0 auto;
  padding: 0 map-get($spacers, 4);
}

// -----------------------------------------------------------------------------
// Grid System
// -----------------------------------------------------------------------------

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-#{$grid-gutter-width} / 2);
}

.col {
  flex: 1 0 0%;
  padding: 0 calc(#{$grid-gutter-width} / 2);
}

// Generate column classes
@for $i from 1 through $grid-columns {
  .col-#{$i} {
    flex: 0 0 percentage($i / $grid-columns);
    max-width: percentage($i / $grid-columns);
    padding: 0 calc(#{$grid-gutter-width} / 2);
  }
}

// Responsive columns
@each $breakpoint, $value in $breakpoints {
  @if $value > 0 {
    @include media-breakpoint-up($breakpoint) {
      @for $i from 1 through $grid-columns {
        .col-#{$breakpoint}-#{$i} {
          flex: 0 0 percentage($i / $grid-columns);
          max-width: percentage($i / $grid-columns);
          padding: 0 calc(#{$grid-gutter-width} / 2);
        }
      }
    }
  }
}

// -----------------------------------------------------------------------------
// Flexbox Utilities
// -----------------------------------------------------------------------------

.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-baseline {
  align-items: baseline;
}

.align-items-stretch {
  align-items: stretch;
}

// -----------------------------------------------------------------------------
// CSS Grid Utilities
// -----------------------------------------------------------------------------

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-cols-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.gap-1 {
  gap: map-get($spacers, 1);
}

.gap-2 {
  gap: map-get($spacers, 2);
}

.gap-3 {
  gap: map-get($spacers, 3);
}

.gap-4 {
  gap: map-get($spacers, 4);
}

.gap-6 {
  gap: map-get($spacers, 6);
}

.gap-8 {
  gap: map-get($spacers, 8);
}
