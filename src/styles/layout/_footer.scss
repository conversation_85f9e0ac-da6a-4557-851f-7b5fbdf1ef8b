// =============================================================================
// FOOTER
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Enhanced Professional Footer
// -----------------------------------------------------------------------------

.footer {
  background: linear-gradient(135deg, $primary-700 0%, $primary-800 50%, $primary-900 100%);
  color: $text-light;
  padding: map-get($spacers, 16) 0 map-get($spacers, 8);
  margin-top: auto;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.03;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, $accent-color 50%, transparent 100%);
  }
}

.footer-content {
  @include container;
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr;
  gap: map-get($spacers, 10);
  margin-bottom: map-get($spacers, 10);
  position: relative;
  z-index: 1;

  @include media-breakpoint-down(lg) {
    grid-template-columns: repeat(2, 1fr);
    gap: map-get($spacers, 8);
  }

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(2, 1fr);
    gap: map-get($spacers, 8);
    margin-bottom: map-get($spacers, 8);
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 6);
    margin-bottom: map-get($spacers, 6);
  }
}

.footer-section {
  h3 {
    color: $text-light;
    margin-bottom: map-get($spacers, 5);
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    letter-spacing: $letter-spacing-wide;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: 40px;
      height: 3px;
      background: linear-gradient(90deg, $accent-color 0%, $accent-light 100%);
      border-radius: 2px;
    }
  }

  p {
    margin-bottom: map-get($spacers, 3);
    line-height: $line-height-relaxed;
    opacity: 0.9;
    font-size: $font-size-md;
    max-width: 300px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: map-get($spacers, 3);
      position: relative;
      padding-left: map-get($spacers, 4);

      &::before {
        content: '→';
        position: absolute;
        left: 0;
        color: $accent-color;
        font-weight: $font-weight-bold;
        transition: transform $animation-duration-base ease;
      }

      &:hover::before {
        transform: translateX(4px);
      }

      a {
        color: rgba($text-light, 0.85);
        text-decoration: none;
        transition: all $animation-duration-base ease;
        font-weight: $font-weight-medium;

        &:hover,
        &:focus {
          color: $accent-color;
          text-decoration: none;
          transform: translateX(2px);
        }

        &:focus-visible {
          outline: 2px solid $accent-color;
          outline-offset: 2px;
          border-radius: $border-radius-sm;
        }
      }
    }
  }
}

.footer-bottom {
  @include container;
  padding-top: map-get($spacers, 8);
  border-top: 1px solid rgba($text-light, 0.15);
  text-align: center;
  position: relative;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, $accent-color 50%, transparent 100%);
  }

  p {
    margin: 0;
    opacity: 0.8;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    letter-spacing: $letter-spacing-wide;

    span {
      color: rgba($accent-color, 0.8);
    }
  }
}

// Enhanced responsive design
@include media-breakpoint-down(md) {
  .footer {
    padding: map-get($spacers, 12) 0 map-get($spacers, 6);
  }

  .footer-section {
    h3 {
      font-size: $font-size-lg;
      margin-bottom: map-get($spacers, 4);
    }

    p {
      font-size: $font-size-base;
      max-width: none;
    }
  }
}

@include media-breakpoint-down(sm) {
  .footer {
    padding: map-get($spacers, 10) 0 map-get($spacers, 5);
  }

  .footer-section {
    text-align: center;

    h3::after {
      left: 50%;
      transform: translateX(-50%);
    }

    ul li {
      text-align: left;
    }
  }

  .footer-bottom {
    padding-top: map-get($spacers, 6);
  }
}
