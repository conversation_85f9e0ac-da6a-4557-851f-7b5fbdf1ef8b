// =============================================================================
// CARDS
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Enhanced Professional Card
// -----------------------------------------------------------------------------

.card {
  background-color: $bg-primary;
  border: 1px solid $border-light;
  border-radius: $border-radius-lg;
  overflow: hidden;
  transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
  box-shadow: $shadow-sm;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary-color 0%, $accent-color 100%);
    opacity: 0;
    transition: opacity $animation-duration-base ease;
  }

  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-4px);
    border-color: $border-color;

    &::before {
      opacity: 1;
    }
  }

  &:focus-within {
    box-shadow: $shadow-primary;
    border-color: $primary-color;
  }
}

.card-header {
  padding: map-get($spacers, 4) map-get($spacers, 5);
  border-bottom: 1px solid $border-light;
  background-color: $bg-secondary;
  
  h3,
  h4,
  h5 {
    margin-bottom: 0;
  }
}

.card-body {
  padding: map-get($spacers, 5);
}

.card-footer {
  padding: map-get($spacers, 4) map-get($spacers, 5);
  border-top: 1px solid $border-light;
  background-color: $bg-secondary;
}

// -----------------------------------------------------------------------------
// Enhanced Service Cards
// -----------------------------------------------------------------------------

.service-card {
  @extend .card;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  background: linear-gradient(145deg, $bg-primary 0%, $bg-secondary 100%);

  &:hover {
    transform: translateY(-6px);
    box-shadow: $shadow-xl;

    .service-icon {
      transform: scale(1.05) rotate(5deg);
      box-shadow: $shadow-primary;
    }
  }
}

.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 88px;
  height: 88px;
  background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
  color: $text-light;
  border-radius: 50%;
  font-size: $font-size-3xl;
  margin: map-get($spacers, 6) auto map-get($spacers, 5);
  transition: all $animation-duration-slow $animation-curve-fast-out-slow-in;
  box-shadow: $shadow-md;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, $primary-200, $accent-color);
    opacity: 0;
    transition: opacity $animation-duration-base ease;
    z-index: -1;
  }

  .service-card:hover &::before {
    opacity: 0.2;
  }
}

.service-content {
  padding: map-get($spacers, 8) map-get($spacers, 6) map-get($spacers, 6);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  text-align: center;

  h3 {
    color: $primary-color;
    margin-bottom: map-get($spacers, 4);
    font-size: $font-size-2xl;
    font-weight: $font-weight-semibold;
    line-height: $line-height-tight;
    letter-spacing: $letter-spacing-tight;
  }

  .service-intro {
    color: $text-secondary;
    margin-bottom: map-get($spacers, 5);
    line-height: $line-height-relaxed;
    font-size: $font-size-md;
  }

  .service-details {
    list-style: none;
    padding: 0;
    margin-bottom: map-get($spacers, 8);
    text-align: left;

    li {
      margin-bottom: map-get($spacers, 4);
      padding-left: map-get($spacers, 7);
      position: relative;
      line-height: $line-height-relaxed;
      font-size: $font-size-sm;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.4em;
        width: 16px;
        height: 16px;
        background: linear-gradient(135deg, $success-color 0%, $success-light 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &::after {
        content: '✓';
        position: absolute;
        left: 4px;
        top: 0.4em;
        color: white;
        font-size: 10px;
        font-weight: $font-weight-bold;
        line-height: 1;
      }

      strong {
        color: $primary-color;
        font-weight: $font-weight-semibold;
      }
    }
  }

  .btn {
    margin-top: auto;
    align-self: center;
    min-width: 140px;
  }
}

// -----------------------------------------------------------------------------
// Services Grid
// -----------------------------------------------------------------------------

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: map-get($spacers, 8);
  margin-top: map-get($spacers, 8);
  
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 6);
  }
}

// -----------------------------------------------------------------------------
// Card Variants
// -----------------------------------------------------------------------------

.card--elevated {
  @include card-shadow(2);
  
  &:hover {
    @include card-shadow(4);
  }
}

.card--bordered {
  border: 2px solid $border-color;
}

.card--primary {
  border-color: $primary-color;
  
  .card-header {
    background-color: $primary-color;
    color: $text-light;
    border-bottom-color: $primary-color;
  }
}

.card--accent {
  border-color: $accent-color;
  
  .card-header {
    background-color: $accent-color;
    color: $text-primary;
    border-bottom-color: $accent-color;
  }
}

// -----------------------------------------------------------------------------
// Card Image
// -----------------------------------------------------------------------------

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  
  &-top {
    border-radius: $border-radius $border-radius 0 0;
  }
  
  &-bottom {
    border-radius: 0 0 $border-radius $border-radius;
  }
}

// -----------------------------------------------------------------------------
// Card Actions
// -----------------------------------------------------------------------------

.card-actions {
  display: flex;
  gap: map-get($spacers, 3);
  align-items: center;
  
  &--end {
    justify-content: flex-end;
  }
  
  &--center {
    justify-content: center;
  }
  
  &--between {
    justify-content: space-between;
  }
}

// -----------------------------------------------------------------------------
// Responsive Cards
// -----------------------------------------------------------------------------

@include media-breakpoint-down(md) {
  .service-content {
    padding: map-get($spacers, 4);
    
    h3 {
      font-size: $font-size-lg;
    }
  }
  
  .service-icon {
    width: 60px;
    height: 60px;
    font-size: $font-size-2xl;
  }
}

@include media-breakpoint-down(sm) {
  .card-body {
    padding: map-get($spacers, 4);
  }
  
  .card-header,
  .card-footer {
    padding: map-get($spacers, 3) map-get($spacers, 4);
  }
}
