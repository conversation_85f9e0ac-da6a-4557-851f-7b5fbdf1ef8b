// =============================================================================
// BUTTONS
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Base Button
// -----------------------------------------------------------------------------

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $btn-padding-y $btn-padding-x;
  margin: 0;
  font-family: inherit;
  font-size: $font-size-base;
  font-weight: $btn-font-weight;
  line-height: $line-height-normal;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: $btn-border-width solid transparent;
  border-radius: $btn-border-radius;
  transition: all $transition-base;
  min-height: 44px; // Accessibility: minimum touch target
  min-width: 44px;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
  }

  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  // Icon spacing
  .icon {
    margin-right: 0.5rem;
    
    &:last-child {
      margin-right: 0;
      margin-left: 0.5rem;
    }
    
    &:only-child {
      margin: 0;
    }
  }
}

// -----------------------------------------------------------------------------
// Button Variants
// -----------------------------------------------------------------------------

// Enhanced Primary Button
.btn--primary {
  color: $text-light;
  background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
  border-color: $primary-color;
  box-shadow: $shadow-sm;
  font-weight: $font-weight-semibold;
  letter-spacing: $letter-spacing-wide;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, $primary-600 0%, $primary-700 100%);
    border-color: $primary-600;
    transform: translateY(-3px);
    box-shadow: $shadow-primary;
  }

  &:active {
    background: linear-gradient(135deg, $primary-700 0%, $primary-800 100%);
    border-color: $primary-700;
    transform: translateY(-1px);
    box-shadow: $shadow-sm;
  }

  &:focus-visible {
    box-shadow: $shadow-primary, 0 0 0 3px rgba($primary-color, 0.3);
  }
}

// Enhanced Secondary Button
.btn--secondary {
  color: $primary-color;
  background-color: $bg-primary;
  border-color: $primary-color;
  font-weight: $font-weight-medium;
  letter-spacing: $letter-spacing-wide;
  box-shadow: $shadow-xs;

  &:hover:not(:disabled) {
    color: $text-light;
    background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
    border-color: $primary-color;
    transform: translateY(-3px);
    box-shadow: $shadow-primary;
  }

  &:active {
    background: linear-gradient(135deg, $primary-600 0%, $primary-700 100%);
    border-color: $primary-600;
    transform: translateY(-1px);
  }

  &:focus-visible {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
  }
}

// Accent Button
.btn--accent {
  color: $text-primary;
  background-color: $accent-color;
  border-color: $accent-color;

  &:hover:not(:disabled) {
    background-color: $accent-dark;
    border-color: $accent-dark;
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  &:active {
    background-color: darken($accent-color, 15%);
    border-color: darken($accent-color, 15%);
    transform: translateY(0);
  }
}

// Success Button
.btn--success {
  color: $text-light;
  background-color: $success-color;
  border-color: $success-color;

  &:hover:not(:disabled) {
    background-color: darken($success-color, 10%);
    border-color: darken($success-color, 10%);
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
}

// Danger Button
.btn--danger {
  color: $text-light;
  background-color: $error-color;
  border-color: $error-color;

  &:hover:not(:disabled) {
    background-color: darken($error-color, 10%);
    border-color: darken($error-color, 10%);
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }
}

// Ghost Button
.btn--ghost {
  color: $text-secondary;
  background-color: transparent;
  border-color: transparent;

  &:hover:not(:disabled) {
    color: $primary-color;
    background-color: rgba($primary-color, 0.1);
  }
}

// -----------------------------------------------------------------------------
// Button Sizes
// -----------------------------------------------------------------------------

.btn--sm {
  padding: $btn-padding-y-sm $btn-padding-x-sm;
  font-size: $btn-font-size-sm;
  min-height: 36px;
}

.btn--lg {
  padding: $btn-padding-y-lg $btn-padding-x-lg;
  font-size: $btn-font-size-lg;
  min-height: 52px;
}

// -----------------------------------------------------------------------------
// Button States
// -----------------------------------------------------------------------------

.btn--loading {
  position: relative;
  color: transparent !important;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: btn-loading 0.8s linear infinite;
  }
}

@keyframes btn-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// -----------------------------------------------------------------------------
// Button Groups
// -----------------------------------------------------------------------------

.btn-group {
  display: inline-flex;
  vertical-align: middle;

  .btn {
    position: relative;
    flex: 1 1 auto;

    &:not(:first-child) {
      margin-left: -$btn-border-width;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &:not(:last-child) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &:hover,
    &:focus,
    &:active {
      z-index: 1;
    }
  }
}

// -----------------------------------------------------------------------------
// Responsive Utilities
// -----------------------------------------------------------------------------

@include media-breakpoint-down(sm) {
  .btn {
    width: 100%;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .btn-group {
    flex-direction: column;
    width: 100%;

    .btn {
      &:not(:first-child) {
        margin-left: 0;
        margin-top: -$btn-border-width;
        border-radius: 0;
      }

      &:first-child {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }
}
