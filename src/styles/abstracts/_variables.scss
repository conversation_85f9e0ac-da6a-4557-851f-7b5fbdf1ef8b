// =============================================================================
// VARIABLES
// =============================================================================

// -----------------------------------------------------------------------------
// Colors
// -----------------------------------------------------------------------------

// Enhanced Brand Colors
$primary-color: #0056b3;
$primary-50: #e6f2ff;
$primary-100: #b3d9ff;
$primary-200: #80c0ff;
$primary-300: #4da6ff;
$primary-400: #1a8dff;
$primary-500: #0056b3;
$primary-600: #004a99;
$primary-700: #003d80;
$primary-800: #003166;
$primary-900: #00244d;
$primary-light: $primary-300;
$primary-dark: $primary-700;

$secondary-color: #f8f9fa;
$secondary-light: #ffffff;
$secondary-dark: #e9ecef;

$accent-color: #ff6b35;
$accent-light: #ff8a5c;
$accent-dark: #e55a2b;

// Enhanced Semantic Colors
$success-color: #10b981;
$success-light: #34d399;
$success-dark: #059669;

$warning-color: #f59e0b;
$warning-light: #fbbf24;
$warning-dark: #d97706;

$error-color: #ef4444;
$error-light: #f87171;
$error-dark: #dc2626;

$info-color: #3b82f6;
$info-light: #60a5fa;
$info-dark: #2563eb;

// Enhanced Text Colors
$text-primary: #111827;
$text-secondary: #6b7280;
$text-muted: #9ca3af;
$text-light: #ffffff;
$text-inverse: #f9fafb;

// Enhanced Background Colors
$bg-primary: #ffffff;
$bg-secondary: #f9fafb;
$bg-tertiary: #f3f4f6;
$bg-dark: #1f2937;
$bg-darker: #111827;

// Enhanced Border Colors
$border-color: #e5e7eb;
$border-light: #f3f4f6;
$border-dark: #d1d5db;
$border-darker: #9ca3af;

// -----------------------------------------------------------------------------
// Typography
// -----------------------------------------------------------------------------

// Font Families
$font-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
$font-heading: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
$font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

// Enhanced Font Sizes with Better Scale
$font-size-xs: 0.75rem;     // 12px
$font-size-sm: 0.875rem;    // 14px
$font-size-base: 1rem;      // 16px
$font-size-md: 1.0625rem;   // 17px - New intermediate size
$font-size-lg: 1.125rem;    // 18px
$font-size-xl: 1.25rem;     // 20px
$font-size-2xl: 1.5rem;     // 24px
$font-size-3xl: 1.875rem;   // 30px
$font-size-4xl: 2.25rem;    // 36px
$font-size-5xl: 3rem;       // 48px
$font-size-6xl: 3.75rem;    // 60px - New larger size

// Enhanced Font Weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;

// Enhanced Line Heights for Better Readability
$line-height-none: 1;
$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

// Letter Spacing for Professional Typography
$letter-spacing-tighter: -0.05em;
$letter-spacing-tight: -0.025em;
$letter-spacing-normal: 0;
$letter-spacing-wide: 0.025em;
$letter-spacing-wider: 0.05em;
$letter-spacing-widest: 0.1em;

// -----------------------------------------------------------------------------
// Enhanced Spacing System
// -----------------------------------------------------------------------------

$spacer: 1rem;

$spacers: (
  0: 0,
  1: $spacer * 0.25,    // 4px
  2: $spacer * 0.5,     // 8px
  3: $spacer * 0.75,    // 12px
  4: $spacer,           // 16px
  5: $spacer * 1.25,    // 20px
  6: $spacer * 1.5,     // 24px
  7: $spacer * 1.75,    // 28px - New intermediate size
  8: $spacer * 2,       // 32px
  9: $spacer * 2.25,    // 36px - New intermediate size
  10: $spacer * 2.5,    // 40px
  12: $spacer * 3,      // 48px
  14: $spacer * 3.5,    // 56px - New intermediate size
  16: $spacer * 4,      // 64px
  18: $spacer * 4.5,    // 72px - New intermediate size
  20: $spacer * 5,      // 80px
  24: $spacer * 6,      // 96px
  28: $spacer * 7,      // 112px - New intermediate size
  32: $spacer * 8,      // 128px
  40: $spacer * 10,     // 160px - New larger size
  48: $spacer * 12,     // 192px - New larger size
);

// -----------------------------------------------------------------------------
// Breakpoints
// -----------------------------------------------------------------------------

$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// -----------------------------------------------------------------------------
// Enhanced Grid System
// -----------------------------------------------------------------------------

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1280px  // Slightly reduced for better readability
);

$grid-columns: 12;
$grid-gutter-width: 1.5rem;

// Content-specific max widths for better readability
$content-max-widths: (
  text: 65ch,      // For body text
  prose: 75ch,     // For article content
  form: 480px,     // For forms
  card: 400px,     // For cards
  hero: 800px,     // For hero content
  section: 1200px  // For section content
);

// -----------------------------------------------------------------------------
// Components
// -----------------------------------------------------------------------------

// Border Radius
$border-radius-sm: 0.25rem;
$border-radius: 0.375rem;
$border-radius-lg: 0.5rem;
$border-radius-xl: 0.75rem;
$border-radius-2xl: 1rem;
$border-radius-full: 9999px;

// Enhanced Professional Shadows
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.06), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
$shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
$shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
$shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.12);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// Colored shadows for interactive elements
$shadow-primary: 0 4px 14px 0 rgba($primary-color, 0.15);
$shadow-accent: 0 4px 14px 0 rgba($accent-color, 0.15);
$shadow-success: 0 4px 14px 0 rgba($success-color, 0.15);

// Transitions
$transition-fast: 150ms ease-in-out;
$transition-base: 250ms ease-in-out;
$transition-slow: 350ms ease-in-out;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// -----------------------------------------------------------------------------
// Forms
// -----------------------------------------------------------------------------

$input-padding-y: 0.75rem;
$input-padding-x: 1rem;
$input-border-width: 1px;
$input-border-color: $border-color;
$input-border-radius: $border-radius;
$input-focus-border-color: $primary-color;
$input-focus-box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);

// -----------------------------------------------------------------------------
// Buttons
// -----------------------------------------------------------------------------

$btn-padding-y: 0.75rem;
$btn-padding-x: 1.5rem;
$btn-border-width: 1px;
$btn-border-radius: $border-radius;
$btn-font-weight: $font-weight-medium;

// Button Sizes
$btn-padding-y-sm: 0.5rem;
$btn-padding-x-sm: 1rem;
$btn-font-size-sm: $font-size-sm;

$btn-padding-y-lg: 1rem;
$btn-padding-x-lg: 2rem;
$btn-font-size-lg: $font-size-lg;

// -----------------------------------------------------------------------------
// Animation & Transitions
// -----------------------------------------------------------------------------

$animation-duration-fast: 150ms;
$animation-duration-base: 250ms;
$animation-duration-slow: 350ms;

$animation-curve-fast-out-slow-in: cubic-bezier(0.4, 0, 0.2, 1);
$animation-curve-linear-out-slow-in: cubic-bezier(0, 0, 0.2, 1);
$animation-curve-fast-out-linear-in: cubic-bezier(0.4, 0, 1, 1);

// -----------------------------------------------------------------------------
// Layout
// -----------------------------------------------------------------------------

$header-height: 70px;
$footer-height: auto;
$sidebar-width: 280px;

// Content widths
$content-width-sm: 540px;
$content-width-md: 720px;
$content-width-lg: 960px;
$content-width-xl: 1140px;
$content-width-xxl: 1320px;
