// -----------------------------------------------------------------------------
// Page-Specific Styles
// -----------------------------------------------------------------------------

// Page Hero for all pages
.page-hero {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  color: $text-light;
  padding: map-get($spacers, 16) 0 map-get($spacers, 12);
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.1;
    pointer-events: none;
  }
  
  .container,
  .container-narrow {
    position: relative;
    z-index: 1;
  }
  
  .hero-title {
    font-size: $font-size-5xl;
    font-weight: $font-weight-extrabold;
    margin-bottom: map-get($spacers, 6);
    line-height: $line-height-tight;
    letter-spacing: $letter-spacing-tight;
    
    @include media-breakpoint-down(md) {
      font-size: $font-size-4xl;
    }
    
    @include media-breakpoint-down(sm) {
      font-size: $font-size-3xl;
    }
  }
  
  .hero-subtitle {
    font-size: $font-size-xl;
    margin-bottom: map-get($spacers, 4);
    opacity: 0.9;
    line-height: $line-height-relaxed;
    
    @include media-breakpoint-down(sm) {
      font-size: $font-size-lg;
    }
  }
  
  .last-updated {
    font-size: $font-size-sm;
    opacity: 0.7;
    font-style: italic;
  }
}

// Services Detail Page
.services-detail {
  padding: map-get($spacers, 20) 0;
  background-color: $bg-secondary;
}

.service-detail-card {
  @extend .service-card;
  margin-bottom: map-get($spacers, 8);
  
  .service-features {
    @extend .service-details;
  }
  
  .service-pricing {
    background: linear-gradient(135deg, $bg-tertiary 0%, $bg-secondary 100%);
    padding: map-get($spacers, 4);
    border-radius: $border-radius;
    margin: map-get($spacers, 6) 0;
    text-align: center;
    
    p {
      margin-bottom: map-get($spacers, 2);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .pricing-note {
      font-size: $font-size-sm;
      color: $text-secondary;
      font-style: italic;
    }
  }
}

// About Page Styles
.about-content {
  padding: map-get($spacers, 20) 0;
}

.about-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: map-get($spacers, 12);
  align-items: start;
  
  @include media-breakpoint-down(lg) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 8);
  }
}

.about-text {
  .lead {
    font-size: $font-size-xl;
    margin-bottom: map-get($spacers, 6);
  }
  
  .feature-list {
    list-style: none;
    padding: 0;
    margin: map-get($spacers, 6) 0;
    
    li {
      display: flex;
      align-items: flex-start;
      margin-bottom: map-get($spacers, 4);
      
      i {
        color: $success-color;
        margin-right: map-get($spacers, 3);
        margin-top: 2px;
        flex-shrink: 0;
      }
    }
  }
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: map-get($spacers, 4);
  
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
  color: $text-light;
  padding: map-get($spacers, 6);
  border-radius: $border-radius-lg;
  text-align: center;
  box-shadow: $shadow-md;
  transition: transform $animation-duration-base ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-lg;
  }
  
  .stat-number {
    font-size: $font-size-4xl;
    font-weight: $font-weight-extrabold;
    line-height: 1;
    margin-bottom: map-get($spacers, 2);
  }
  
  .stat-label {
    font-size: $font-size-sm;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: $letter-spacing-wide;
  }
}

// Team Section
.team-section {
  padding: map-get($spacers, 20) 0;
  background-color: $bg-primary;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: map-get($spacers, 8);
  margin-top: map-get($spacers, 12);
}

.team-member {
  background-color: $bg-primary;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $shadow-sm;
  transition: all $animation-duration-base ease;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-4px);
  }
  
  .member-photo {
    height: 250px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform $animation-duration-base ease;
    }
  }
  
  &:hover .member-photo img {
    transform: scale(1.05);
  }
  
  .member-info {
    padding: map-get($spacers, 6);
    
    h3 {
      color: $primary-color;
      margin-bottom: map-get($spacers, 2);
      font-size: $font-size-xl;
    }
    
    .member-role {
      color: $accent-color;
      font-weight: $font-weight-semibold;
      margin-bottom: map-get($spacers, 3);
      font-size: $font-size-sm;
      text-transform: uppercase;
      letter-spacing: $letter-spacing-wide;
    }
    
    .member-bio {
      color: $text-secondary;
      line-height: $line-height-relaxed;
      font-size: $font-size-sm;
    }
  }
}

// Contact Page Styles
.contact-section {
  padding: map-get($spacers, 20) 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: map-get($spacers, 12);
  
  @include media-breakpoint-down(lg) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 8);
  }
}

.contact-info {
  .contact-intro {
    font-size: $font-size-lg;
    margin-bottom: map-get($spacers, 8);
    color: $text-secondary;
  }
}

.contact-methods {
  .contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: map-get($spacers, 8);
    
    .contact-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
      color: $text-light;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: map-get($spacers, 4);
      flex-shrink: 0;
      font-size: $font-size-xl;
      box-shadow: $shadow-sm;
    }
    
    .contact-details {
      h3 {
        color: $primary-color;
        margin-bottom: map-get($spacers, 2);
        font-size: $font-size-lg;
      }
      
      p {
        margin-bottom: map-get($spacers, 2);
        
        a {
          color: $primary-color;
          text-decoration: none;
          font-weight: $font-weight-semibold;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
      
      .contact-note {
        font-size: $font-size-sm;
        color: $text-secondary;
        font-style: italic;
      }
    }
  }
}

// Testimonials Page Styles
.testimonials-section {
  padding: map-get($spacers, 20) 0;
}

.testimonials-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: map-get($spacers, 6);
  margin-bottom: map-get($spacers, 16);
  
  @include media-breakpoint-down(md) {
    grid-template-columns: 1fr;
    gap: map-get($spacers, 4);
  }
}

.stat-item {
  text-align: center;
  padding: map-get($spacers, 6);
  background: linear-gradient(135deg, $bg-primary 0%, $bg-secondary 100%);
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  
  .stat-number {
    font-size: $font-size-4xl;
    font-weight: $font-weight-extrabold;
    color: $primary-color;
    line-height: 1;
    margin-bottom: map-get($spacers, 2);
  }
  
  .stat-label {
    font-size: $font-size-sm;
    color: $text-secondary;
    text-transform: uppercase;
    letter-spacing: $letter-spacing-wide;
    margin-bottom: map-get($spacers, 3);
  }
  
  .stars {
    color: $accent-color;
    font-size: $font-size-lg;
  }
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: map-get($spacers, 8);
  margin-bottom: map-get($spacers, 16);
  
  @include media-breakpoint-down(sm) {
    grid-template-columns: 1fr;
  }
}

.testimonial-card {
  background-color: $bg-primary;
  border-radius: $border-radius-lg;
  padding: map-get($spacers, 8);
  box-shadow: $shadow-sm;
  transition: all $animation-duration-base ease;
  border-left: 4px solid $accent-color;
  
  &:hover {
    box-shadow: $shadow-lg;
    transform: translateY(-4px);
  }
  
  .stars {
    color: $accent-color;
    margin-bottom: map-get($spacers, 4);
    font-size: $font-size-sm;
  }
  
  blockquote {
    font-size: $font-size-lg;
    line-height: $line-height-relaxed;
    margin-bottom: map-get($spacers, 6);
    font-style: italic;
    color: $text-primary;
    
    &::before,
    &::after {
      content: '"';
      color: $accent-color;
      font-size: $font-size-2xl;
      font-weight: $font-weight-bold;
    }
  }
  
  .testimonial-author {
    .author-name {
      font-weight: $font-weight-semibold;
      color: $primary-color;
      font-size: $font-size-base;
    }
    
    .author-title {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-top: map-get($spacers, 1);
    }
  }
}

.testimonials-cta {
  text-align: center;
  padding: map-get($spacers, 12);
  background: linear-gradient(135deg, $bg-secondary 0%, $bg-tertiary 100%);
  border-radius: $border-radius-lg;
  
  h3 {
    color: $primary-color;
    margin-bottom: map-get($spacers, 4);
    font-size: $font-size-2xl;
  }
  
  p {
    color: $text-secondary;
    margin-bottom: map-get($spacers, 6);
    font-size: $font-size-lg;
  }
}

// Legal Pages (Privacy & Terms)
.privacy-content,
.terms-content {
  padding: map-get($spacers, 16) 0;
}

.privacy-text,
.terms-text {
  h2 {
    color: $primary-color;
    margin-top: map-get($spacers, 8);
    margin-bottom: map-get($spacers, 4);
    font-size: $font-size-2xl;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  h3 {
    color: $primary-color;
    margin-top: map-get($spacers, 6);
    margin-bottom: map-get($spacers, 3);
    font-size: $font-size-xl;
  }
  
  p {
    margin-bottom: map-get($spacers, 4);
    line-height: $line-height-relaxed;
  }
  
  ul {
    margin-bottom: map-get($spacers, 4);
    padding-left: map-get($spacers, 6);
    
    li {
      margin-bottom: map-get($spacers, 2);
      line-height: $line-height-relaxed;
    }
  }
  
  .contact-info {
    background: linear-gradient(135deg, $bg-secondary 0%, $bg-tertiary 100%);
    padding: map-get($spacers, 6);
    border-radius: $border-radius;
    margin: map-get($spacers, 6) 0;
    border-left: 4px solid $primary-color;
    
    p {
      margin-bottom: map-get($spacers, 2);
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    a {
      color: $primary-color;
      text-decoration: none;
      font-weight: $font-weight-semibold;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
