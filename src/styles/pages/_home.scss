// =============================================================================
// HOME PAGE
// =============================================================================

@use '../abstracts/variables' as *;
@use '../abstracts/mixins' as *;

// -----------------------------------------------------------------------------
// Enhanced Hero Section
// -----------------------------------------------------------------------------

.hero {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  color: $text-light;
  padding: map-get($spacers, 20) 0 map-get($spacers, 16);
  text-align: center;
  position: relative;
  overflow: hidden;
  min-height: 70vh;
  display: flex;
  align-items: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.1;
    pointer-events: none;
  }

  // Add subtle gradient overlay for depth
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
    pointer-events: none;
  }

  .container {
    position: relative;
    z-index: 1;
    width: 100%;
  }
}

.hero-content {
  max-width: map-get($content-max-widths, hero);
  margin: 0 auto;
  padding: 0 map-get($spacers, 4);
}

.hero-title {
  font-size: $font-size-6xl;
  font-weight: $font-weight-extrabold;
  margin-bottom: map-get($spacers, 8);
  line-height: $line-height-tight;
  letter-spacing: $letter-spacing-tighter;

  @include media-breakpoint-down(md) {
    font-size: $font-size-5xl;
    margin-bottom: map-get($spacers, 6);
  }

  @include media-breakpoint-down(sm) {
    font-size: $font-size-4xl;
    line-height: $line-height-snug;
  }

  .highlight {
    color: $accent-color;
    position: relative;
    display: inline-block;

    &::after {
      content: '';
      position: absolute;
      bottom: 0.1em;
      left: 0;
      right: 0;
      height: 0.15em;
      background: linear-gradient(90deg, $accent-color 0%, lighten($accent-color, 10%) 100%);
      border-radius: 2px;
      transform: scaleX(0);
      transform-origin: left;
      animation: underlineGrow 0.8s ease-out 0.5s forwards;
    }
  }
}

@keyframes underlineGrow {
  to {
    transform: scaleX(1);
  }
}

.hero-subtitle {
  font-size: $font-size-2xl;
  margin-bottom: map-get($spacers, 10);
  opacity: 0.95;
  line-height: $line-height-relaxed;
  font-weight: $font-weight-normal;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @include media-breakpoint-down(md) {
    font-size: $font-size-xl;
    margin-bottom: map-get($spacers, 8);
  }

  @include media-breakpoint-down(sm) {
    font-size: $font-size-lg;
  }
}

.hero-buttons {
  display: flex;
  gap: map-get($spacers, 5);
  justify-content: center;
  flex-wrap: wrap;
  margin-top: map-get($spacers, 2);

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    align-items: center;
    gap: map-get($spacers, 4);
  }
}

// -----------------------------------------------------------------------------
// Enhanced Services Overview Section
// -----------------------------------------------------------------------------

.services-overview {
  padding: map-get($spacers, 24) 0;
  background: linear-gradient(180deg, $bg-primary 0%, $bg-secondary 50%, $bg-primary 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.03;
    pointer-events: none;
  }
}

.section-title {
  text-align: center;
  margin-bottom: map-get($spacers, 16);
  max-width: map-get($content-max-widths, hero);
  margin-left: auto;
  margin-right: auto;

  &:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) {
    font-size: $font-size-4xl;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin-bottom: map-get($spacers, 6);
    letter-spacing: $letter-spacing-tight;
    line-height: $line-height-tight;

    @include media-breakpoint-down(md) {
      font-size: $font-size-3xl;
      margin-bottom: map-get($spacers, 5);
    }

    @include media-breakpoint-down(sm) {
      font-size: $font-size-2xl;
      margin-bottom: map-get($spacers, 4);
    }
  }

  .section-subtitle {
    font-size: $font-size-xl;
    color: $text-secondary;
    max-width: 600px;
    margin: 0 auto;
    line-height: $line-height-relaxed;
    font-weight: $font-weight-normal;

    @include media-breakpoint-down(sm) {
      font-size: $font-size-lg;
    }
  }
}

// -----------------------------------------------------------------------------
// Enhanced FAQ Section
// -----------------------------------------------------------------------------

.faq-section {
  padding: map-get($spacers, 24) 0;
  background: linear-gradient(180deg, $bg-primary 0%, $bg-secondary 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/patterns/grain.svg');
    opacity: 0.02;
    pointer-events: none;
  }
}

.faq-list {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 map-get($spacers, 4);
}

.faq-item {
  margin-bottom: map-get($spacers, 5);
  border-radius: $border-radius-lg;
  overflow: hidden;
  background-color: $bg-primary;
  border: 1px solid $border-light;
  box-shadow: $shadow-sm;
  transition: all $animation-duration-base $animation-curve-fast-out-slow-in;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    box-shadow: $shadow-md;
    transform: translateY(-2px);
  }
}

.faq-question {
  width: 100%;
  padding: map-get($spacers, 6) map-get($spacers, 8) map-get($spacers, 6) map-get($spacers, 6);
  text-align: left;
  font-weight: $font-weight-semibold;
  font-size: $font-size-lg;
  color: $primary-color;
  background-color: $bg-primary;
  border: none;
  cursor: pointer;
  transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
  position: relative;
  line-height: $line-height-snug;
  letter-spacing: $letter-spacing-normal;

  &::after {
    content: '';
    position: absolute;
    right: map-get($spacers, 6);
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
    border-radius: 50%;
    transition: all $animation-duration-base $animation-curve-fast-out-slow-in;
  }

  &::before {
    content: '+';
    position: absolute;
    right: map-get($spacers, 6) + 7px;
    top: 50%;
    transform: translateY(-50%);
    color: $text-light;
    font-size: $font-size-lg;
    font-weight: $font-weight-normal;
    transition: transform $animation-duration-base $animation-curve-fast-out-slow-in;
    z-index: 1;
  }

  &:hover {
    background: linear-gradient(135deg, $bg-secondary 0%, $bg-tertiary 100%);
    color: $primary-600;

    &::after {
      background: linear-gradient(135deg, $primary-600 0%, $primary-700 100%);
      transform: translateY(-50%) scale(1.1);
    }
  }

  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  &.active {
    background: linear-gradient(135deg, $primary-color 0%, $primary-600 100%);
    color: $text-light;

    &::after {
      background: linear-gradient(135deg, $accent-color 0%, $accent-dark 100%);
    }

    &::before {
      transform: translateY(-50%) rotate(45deg);
    }
  }
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height $animation-duration-slow $animation-curve-fast-out-slow-in;
  background-color: $bg-secondary;

  &.active {
    max-height: 300px;
  }

  p {
    padding: map-get($spacers, 5) map-get($spacers, 6) map-get($spacers, 6);
    margin: 0;
    color: $text-secondary;
    line-height: $line-height-relaxed;
    font-size: $font-size-md;
    border-top: 1px solid $border-light;
  }
}

// -----------------------------------------------------------------------------
// Enhanced Professional Animations
// -----------------------------------------------------------------------------

.fade-in-section {
  opacity: 0;
  transform: translateY(40px);
  transition: all 0.8s $animation-curve-fast-out-slow-in;

  &.animate-in {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-40px);
  transition: all 0.8s $animation-curve-fast-out-slow-in;

  &.animate-in {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  opacity: 0;
  transform: translateX(40px);
  transition: all 0.8s $animation-curve-fast-out-slow-in;

  &.animate-in {
    opacity: 1;
    transform: translateX(0);
  }
}

.scale-in {
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.6s $animation-curve-fast-out-slow-in;

  &.animate-in {
    opacity: 1;
    transform: scale(1);
  }
}

// Staggered animation delays for multiple elements
.animate-delay-1 { transition-delay: 0.1s; }
.animate-delay-2 { transition-delay: 0.2s; }
.animate-delay-3 { transition-delay: 0.3s; }
.animate-delay-4 { transition-delay: 0.4s; }

// Hover animations for interactive elements
.hover-lift {
  transition: transform $animation-duration-base $animation-curve-fast-out-slow-in;

  &:hover {
    transform: translateY(-4px);
  }
}

.hover-scale {
  transition: transform $animation-duration-base $animation-curve-fast-out-slow-in;

  &:hover {
    transform: scale(1.05);
  }
}

// Pulse animation for call-to-action elements
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse-on-hover {
  &:hover {
    animation: pulse 1s ease-in-out infinite;
  }
}

// -----------------------------------------------------------------------------
// Enhanced Responsive Design
// -----------------------------------------------------------------------------

@include media-breakpoint-down(lg) {
  .hero {
    padding: map-get($spacers, 16) 0 map-get($spacers, 12);
    min-height: 60vh;
  }

  .hero-title {
    font-size: $font-size-5xl;
  }

  .services-overview,
  .faq-section {
    padding: map-get($spacers, 18) 0;
  }
}

@include media-breakpoint-down(md) {
  .hero {
    padding: map-get($spacers, 12) 0 map-get($spacers, 10);
    min-height: 50vh;
  }

  .hero-title {
    font-size: $font-size-4xl;
    margin-bottom: map-get($spacers, 6);
  }

  .hero-subtitle {
    font-size: $font-size-xl;
    margin-bottom: map-get($spacers, 8);
  }

  .services-overview,
  .faq-section {
    padding: map-get($spacers, 14) 0;
  }

  .section-title {
    margin-bottom: map-get($spacers, 10);

    &:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) {
      font-size: $font-size-3xl;
    }
  }

  .service-icon {
    width: 72px;
    height: 72px;
    font-size: $font-size-2xl;
  }
}

@include media-breakpoint-down(sm) {
  .hero {
    padding: map-get($spacers, 10) 0 map-get($spacers, 8);
    min-height: auto;
  }

  .hero-title {
    font-size: $font-size-3xl;
    line-height: $line-height-snug;
    margin-bottom: map-get($spacers, 5);
  }

  .hero-subtitle {
    font-size: $font-size-lg;
    margin-bottom: map-get($spacers, 6);
  }

  .hero-buttons {
    gap: map-get($spacers, 3);
  }

  .services-overview,
  .faq-section {
    padding: map-get($spacers, 10) 0;
  }

  .section-title {
    margin-bottom: map-get($spacers, 8);

    &:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) {
      font-size: $font-size-2xl;
    }

    .section-subtitle {
      font-size: $font-size-base;
    }
  }

  .service-icon {
    width: 64px;
    height: 64px;
    font-size: $font-size-xl;
    margin: map-get($spacers, 5) auto map-get($spacers, 4);
  }

  .service-content {
    padding: map-get($spacers, 5) map-get($spacers, 4) map-get($spacers, 4);

    h3 {
      font-size: $font-size-xl;
      margin-bottom: map-get($spacers, 3);
    }

    .service-intro {
      font-size: $font-size-base;
      margin-bottom: map-get($spacers, 4);
    }

    .service-details {
      margin-bottom: map-get($spacers, 6);

      li {
        margin-bottom: map-get($spacers, 3);
        padding-left: map-get($spacers, 6);
        font-size: $font-size-xs;

        &::before {
          width: 14px;
          height: 14px;
        }

        &::after {
          left: 3px;
          font-size: 9px;
        }
      }
    }
  }

  .faq-question {
    padding: map-get($spacers, 5) map-get($spacers, 7) map-get($spacers, 5) map-get($spacers, 5);
    font-size: $font-size-base;

    &::after {
      right: map-get($spacers, 5);
      width: 20px;
      height: 20px;
    }

    &::before {
      right: map-get($spacers, 5) + 6px;
      font-size: $font-size-base;
    }
  }

  .faq-answer p {
    padding: map-get($spacers, 4) map-get($spacers, 5) map-get($spacers, 5);
    font-size: $font-size-sm;
  }

  .faq-list {
    padding: 0 map-get($spacers, 3);
  }
}

// Extra small devices
@include media-breakpoint-down(xs) {
  .hero-content {
    padding: 0 map-get($spacers, 3);
  }

  .hero-title {
    font-size: $font-size-2xl;
  }

  .hero-subtitle {
    font-size: $font-size-base;
  }

  .service-content {
    padding: map-get($spacers, 4) map-get($spacers, 3) map-get($spacers, 3);
  }
}
