{"version": 3, "sources": ["../../intersection-observer/intersection-observer.js"], "sourcesContent": ["/**\n * Copyright 2016 Google Inc. All Rights Reserved.\n *\n * Licensed under the W3C SOFTWARE AND DOCUMENT NOTICE AND LICENSE.\n *\n *  https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n *\n */\n(function() {\n'use strict';\n\n// Exit early if we're not running in a browser.\nif (typeof window !== 'object') {\n  return;\n}\n\n// Exit early if all IntersectionObserver and IntersectionObserverEntry\n// features are natively supported.\nif ('IntersectionObserver' in window &&\n    'IntersectionObserverEntry' in window &&\n    'intersectionRatio' in window.IntersectionObserverEntry.prototype) {\n\n  // Minimal polyfill for Edge 15's lack of `isIntersecting`\n  // See: https://github.com/w3c/IntersectionObserver/issues/211\n  if (!('isIntersecting' in window.IntersectionObserverEntry.prototype)) {\n    Object.defineProperty(window.IntersectionObserverEntry.prototype,\n      'isIntersecting', {\n      get: function () {\n        return this.intersectionRatio > 0;\n      }\n    });\n  }\n  return;\n}\n\n/**\n * Returns the embedding frame element, if any.\n * @param {!Document} doc\n * @return {!Element}\n */\nfunction getFrameElement(doc) {\n  try {\n    return doc.defaultView && doc.defaultView.frameElement || null;\n  } catch (e) {\n    // Ignore the error.\n    return null;\n  }\n}\n\n/**\n * A local reference to the root document.\n */\nvar document = (function(startDoc) {\n  var doc = startDoc;\n  var frame = getFrameElement(doc);\n  while (frame) {\n    doc = frame.ownerDocument;\n    frame = getFrameElement(doc);\n  }\n  return doc;\n})(window.document);\n\n/**\n * An IntersectionObserver registry. This registry exists to hold a strong\n * reference to IntersectionObserver instances currently observing a target\n * element. Without this registry, instances without another reference may be\n * garbage collected.\n */\nvar registry = [];\n\n/**\n * The signal updater for cross-origin intersection. When not null, it means\n * that the polyfill is configured to work in a cross-origin mode.\n * @type {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nvar crossOriginUpdater = null;\n\n/**\n * The current cross-origin intersection. Only used in the cross-origin mode.\n * @type {DOMRect|ClientRect}\n */\nvar crossOriginRect = null;\n\n\n/**\n * Creates the global IntersectionObserverEntry constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-entry\n * @param {Object} entry A dictionary of instance properties.\n * @constructor\n */\nfunction IntersectionObserverEntry(entry) {\n  this.time = entry.time;\n  this.target = entry.target;\n  this.rootBounds = ensureDOMRect(entry.rootBounds);\n  this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);\n  this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());\n  this.isIntersecting = !!entry.intersectionRect;\n\n  // Calculates the intersection ratio.\n  var targetRect = this.boundingClientRect;\n  var targetArea = targetRect.width * targetRect.height;\n  var intersectionRect = this.intersectionRect;\n  var intersectionArea = intersectionRect.width * intersectionRect.height;\n\n  // Sets intersection ratio.\n  if (targetArea) {\n    // Round the intersection ratio to avoid floating point math issues:\n    // https://github.com/w3c/IntersectionObserver/issues/324\n    this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));\n  } else {\n    // If area is zero and is intersecting, sets to 1, otherwise to 0\n    this.intersectionRatio = this.isIntersecting ? 1 : 0;\n  }\n}\n\n\n/**\n * Creates the global IntersectionObserver constructor.\n * https://w3c.github.io/IntersectionObserver/#intersection-observer-interface\n * @param {Function} callback The function to be invoked after intersection\n *     changes have queued. The function is not invoked if the queue has\n *     been emptied by calling the `takeRecords` method.\n * @param {Object=} opt_options Optional configuration options.\n * @constructor\n */\nfunction IntersectionObserver(callback, opt_options) {\n\n  var options = opt_options || {};\n\n  if (typeof callback != 'function') {\n    throw new Error('callback must be a function');\n  }\n\n  if (\n    options.root &&\n    options.root.nodeType != 1 &&\n    options.root.nodeType != 9\n  ) {\n    throw new Error('root must be a Document or Element');\n  }\n\n  // Binds and throttles `this._checkForIntersections`.\n  this._checkForIntersections = throttle(\n      this._checkForIntersections.bind(this), this.THROTTLE_TIMEOUT);\n\n  // Private properties.\n  this._callback = callback;\n  this._observationTargets = [];\n  this._queuedEntries = [];\n  this._rootMarginValues = this._parseRootMargin(options.rootMargin);\n\n  // Public properties.\n  this.thresholds = this._initThresholds(options.threshold);\n  this.root = options.root || null;\n  this.rootMargin = this._rootMarginValues.map(function(margin) {\n    return margin.value + margin.unit;\n  }).join(' ');\n\n  /** @private @const {!Array<!Document>} */\n  this._monitoringDocuments = [];\n  /** @private @const {!Array<function()>} */\n  this._monitoringUnsubscribes = [];\n}\n\n\n/**\n * The minimum interval within which the document will be checked for\n * intersection changes.\n */\nIntersectionObserver.prototype.THROTTLE_TIMEOUT = 100;\n\n\n/**\n * The frequency in which the polyfill polls for intersection changes.\n * this can be updated on a per instance basis and must be set prior to\n * calling `observe` on the first target.\n */\nIntersectionObserver.prototype.POLL_INTERVAL = null;\n\n/**\n * Use a mutation observer on the root element\n * to detect intersection changes.\n */\nIntersectionObserver.prototype.USE_MUTATION_OBSERVER = true;\n\n\n/**\n * Sets up the polyfill in the cross-origin mode. The result is the\n * updater function that accepts two arguments: `boundingClientRect` and\n * `intersectionRect` - just as these fields would be available to the\n * parent via `IntersectionObserverEntry`. This function should be called\n * each time the iframe receives intersection information from the parent\n * window, e.g. via messaging.\n * @return {function(DOMRect|ClientRect, DOMRect|ClientRect)}\n */\nIntersectionObserver._setupCrossOriginUpdater = function() {\n  if (!crossOriginUpdater) {\n    /**\n     * @param {DOMRect|ClientRect} boundingClientRect\n     * @param {DOMRect|ClientRect} intersectionRect\n     */\n    crossOriginUpdater = function(boundingClientRect, intersectionRect) {\n      if (!boundingClientRect || !intersectionRect) {\n        crossOriginRect = getEmptyRect();\n      } else {\n        crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);\n      }\n      registry.forEach(function(observer) {\n        observer._checkForIntersections();\n      });\n    };\n  }\n  return crossOriginUpdater;\n};\n\n\n/**\n * Resets the cross-origin mode.\n */\nIntersectionObserver._resetCrossOriginUpdater = function() {\n  crossOriginUpdater = null;\n  crossOriginRect = null;\n};\n\n\n/**\n * Starts observing a target element for intersection changes based on\n * the thresholds values.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.observe = function(target) {\n  var isTargetAlreadyObserved = this._observationTargets.some(function(item) {\n    return item.element == target;\n  });\n\n  if (isTargetAlreadyObserved) {\n    return;\n  }\n\n  if (!(target && target.nodeType == 1)) {\n    throw new Error('target must be an Element');\n  }\n\n  this._registerInstance();\n  this._observationTargets.push({element: target, entry: null});\n  this._monitorIntersections(target.ownerDocument);\n  this._checkForIntersections();\n};\n\n\n/**\n * Stops observing a target element for intersection changes.\n * @param {Element} target The DOM element to observe.\n */\nIntersectionObserver.prototype.unobserve = function(target) {\n  this._observationTargets =\n      this._observationTargets.filter(function(item) {\n        return item.element != target;\n      });\n  this._unmonitorIntersections(target.ownerDocument);\n  if (this._observationTargets.length == 0) {\n    this._unregisterInstance();\n  }\n};\n\n\n/**\n * Stops observing all target elements for intersection changes.\n */\nIntersectionObserver.prototype.disconnect = function() {\n  this._observationTargets = [];\n  this._unmonitorAllIntersections();\n  this._unregisterInstance();\n};\n\n\n/**\n * Returns any queue entries that have not yet been reported to the\n * callback and clears the queue. This can be used in conjunction with the\n * callback to obtain the absolute most up-to-date intersection information.\n * @return {Array} The currently queued entries.\n */\nIntersectionObserver.prototype.takeRecords = function() {\n  var records = this._queuedEntries.slice();\n  this._queuedEntries = [];\n  return records;\n};\n\n\n/**\n * Accepts the threshold value from the user configuration object and\n * returns a sorted array of unique threshold values. If a value is not\n * between 0 and 1 and error is thrown.\n * @private\n * @param {Array|number=} opt_threshold An optional threshold value or\n *     a list of threshold values, defaulting to [0].\n * @return {Array} A sorted list of unique and valid threshold values.\n */\nIntersectionObserver.prototype._initThresholds = function(opt_threshold) {\n  var threshold = opt_threshold || [0];\n  if (!Array.isArray(threshold)) threshold = [threshold];\n\n  return threshold.sort().filter(function(t, i, a) {\n    if (typeof t != 'number' || isNaN(t) || t < 0 || t > 1) {\n      throw new Error('threshold must be a number between 0 and 1 inclusively');\n    }\n    return t !== a[i - 1];\n  });\n};\n\n\n/**\n * Accepts the rootMargin value from the user configuration object\n * and returns an array of the four margin values as an object containing\n * the value and unit properties. If any of the values are not properly\n * formatted or use a unit other than px or %, and error is thrown.\n * @private\n * @param {string=} opt_rootMargin An optional rootMargin value,\n *     defaulting to '0px'.\n * @return {Array<Object>} An array of margin objects with the keys\n *     value and unit.\n */\nIntersectionObserver.prototype._parseRootMargin = function(opt_rootMargin) {\n  var marginString = opt_rootMargin || '0px';\n  var margins = marginString.split(/\\s+/).map(function(margin) {\n    var parts = /^(-?\\d*\\.?\\d+)(px|%)$/.exec(margin);\n    if (!parts) {\n      throw new Error('rootMargin must be specified in pixels or percent');\n    }\n    return {value: parseFloat(parts[1]), unit: parts[2]};\n  });\n\n  // Handles shorthand.\n  margins[1] = margins[1] || margins[0];\n  margins[2] = margins[2] || margins[0];\n  margins[3] = margins[3] || margins[1];\n\n  return margins;\n};\n\n\n/**\n * Starts polling for intersection changes if the polling is not already\n * happening, and if the page's visibility state is visible.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._monitorIntersections = function(doc) {\n  var win = doc.defaultView;\n  if (!win) {\n    // Already destroyed.\n    return;\n  }\n  if (this._monitoringDocuments.indexOf(doc) != -1) {\n    // Already monitoring.\n    return;\n  }\n\n  // Private state for monitoring.\n  var callback = this._checkForIntersections;\n  var monitoringInterval = null;\n  var domObserver = null;\n\n  // If a poll interval is set, use polling instead of listening to\n  // resize and scroll events or DOM mutations.\n  if (this.POLL_INTERVAL) {\n    monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);\n  } else {\n    addEvent(win, 'resize', callback, true);\n    addEvent(doc, 'scroll', callback, true);\n    if (this.USE_MUTATION_OBSERVER && 'MutationObserver' in win) {\n      domObserver = new win.MutationObserver(callback);\n      domObserver.observe(doc, {\n        attributes: true,\n        childList: true,\n        characterData: true,\n        subtree: true\n      });\n    }\n  }\n\n  this._monitoringDocuments.push(doc);\n  this._monitoringUnsubscribes.push(function() {\n    // Get the window object again. When a friendly iframe is destroyed, it\n    // will be null.\n    var win = doc.defaultView;\n\n    if (win) {\n      if (monitoringInterval) {\n        win.clearInterval(monitoringInterval);\n      }\n      removeEvent(win, 'resize', callback, true);\n    }\n\n    removeEvent(doc, 'scroll', callback, true);\n    if (domObserver) {\n      domObserver.disconnect();\n    }\n  });\n\n  // Also monitor the parent.\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._monitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorIntersections = function(doc) {\n  var index = this._monitoringDocuments.indexOf(doc);\n  if (index == -1) {\n    return;\n  }\n\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n\n  // Check if any dependent targets are still remaining.\n  var hasDependentTargets =\n      this._observationTargets.some(function(item) {\n        var itemDoc = item.element.ownerDocument;\n        // Target is in this context.\n        if (itemDoc == doc) {\n          return true;\n        }\n        // Target is nested in this context.\n        while (itemDoc && itemDoc != rootDoc) {\n          var frame = getFrameElement(itemDoc);\n          itemDoc = frame && frame.ownerDocument;\n          if (itemDoc == doc) {\n            return true;\n          }\n        }\n        return false;\n      });\n  if (hasDependentTargets) {\n    return;\n  }\n\n  // Unsubscribe.\n  var unsubscribe = this._monitoringUnsubscribes[index];\n  this._monitoringDocuments.splice(index, 1);\n  this._monitoringUnsubscribes.splice(index, 1);\n  unsubscribe();\n\n  // Also unmonitor the parent.\n  if (doc != rootDoc) {\n    var frame = getFrameElement(doc);\n    if (frame) {\n      this._unmonitorIntersections(frame.ownerDocument);\n    }\n  }\n};\n\n\n/**\n * Stops polling for intersection changes.\n * @param {!Document} doc\n * @private\n */\nIntersectionObserver.prototype._unmonitorAllIntersections = function() {\n  var unsubscribes = this._monitoringUnsubscribes.slice(0);\n  this._monitoringDocuments.length = 0;\n  this._monitoringUnsubscribes.length = 0;\n  for (var i = 0; i < unsubscribes.length; i++) {\n    unsubscribes[i]();\n  }\n};\n\n\n/**\n * Scans each observation target for intersection changes and adds them\n * to the internal entries queue. If new entries are found, it\n * schedules the callback to be invoked.\n * @private\n */\nIntersectionObserver.prototype._checkForIntersections = function() {\n  if (!this.root && crossOriginUpdater && !crossOriginRect) {\n    // Cross origin monitoring, but no initial data available yet.\n    return;\n  }\n\n  var rootIsInDom = this._rootIsInDom();\n  var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();\n\n  this._observationTargets.forEach(function(item) {\n    var target = item.element;\n    var targetRect = getBoundingClientRect(target);\n    var rootContainsTarget = this._rootContainsTarget(target);\n    var oldEntry = item.entry;\n    var intersectionRect = rootIsInDom && rootContainsTarget &&\n        this._computeTargetAndRootIntersection(target, targetRect, rootRect);\n\n    var rootBounds = null;\n    if (!this._rootContainsTarget(target)) {\n      rootBounds = getEmptyRect();\n    } else if (!crossOriginUpdater || this.root) {\n      rootBounds = rootRect;\n    }\n\n    var newEntry = item.entry = new IntersectionObserverEntry({\n      time: now(),\n      target: target,\n      boundingClientRect: targetRect,\n      rootBounds: rootBounds,\n      intersectionRect: intersectionRect\n    });\n\n    if (!oldEntry) {\n      this._queuedEntries.push(newEntry);\n    } else if (rootIsInDom && rootContainsTarget) {\n      // If the new entry intersection ratio has crossed any of the\n      // thresholds, add a new entry.\n      if (this._hasCrossedThreshold(oldEntry, newEntry)) {\n        this._queuedEntries.push(newEntry);\n      }\n    } else {\n      // If the root is not in the DOM or target is not contained within\n      // root but the previous entry for this target had an intersection,\n      // add a new record indicating removal.\n      if (oldEntry && oldEntry.isIntersecting) {\n        this._queuedEntries.push(newEntry);\n      }\n    }\n  }, this);\n\n  if (this._queuedEntries.length) {\n    this._callback(this.takeRecords(), this);\n  }\n};\n\n\n/**\n * Accepts a target and root rect computes the intersection between then\n * following the algorithm in the spec.\n * TODO(philipwalton): at this time clip-path is not considered.\n * https://w3c.github.io/IntersectionObserver/#calculate-intersection-rect-algo\n * @param {Element} target The target DOM element\n * @param {Object} targetRect The bounding rect of the target.\n * @param {Object} rootRect The bounding rect of the root after being\n *     expanded by the rootMargin value.\n * @return {?Object} The final intersection rect object or undefined if no\n *     intersection is found.\n * @private\n */\nIntersectionObserver.prototype._computeTargetAndRootIntersection =\n    function(target, targetRect, rootRect) {\n  // If the element isn't displayed, an intersection can't happen.\n  if (window.getComputedStyle(target).display == 'none') return;\n\n  var intersectionRect = targetRect;\n  var parent = getParentNode(target);\n  var atRoot = false;\n\n  while (!atRoot && parent) {\n    var parentRect = null;\n    var parentComputedStyle = parent.nodeType == 1 ?\n        window.getComputedStyle(parent) : {};\n\n    // If the parent isn't displayed, an intersection can't happen.\n    if (parentComputedStyle.display == 'none') return null;\n\n    if (parent == this.root || parent.nodeType == /* DOCUMENT */ 9) {\n      atRoot = true;\n      if (parent == this.root || parent == document) {\n        if (crossOriginUpdater && !this.root) {\n          if (!crossOriginRect ||\n              crossOriginRect.width == 0 && crossOriginRect.height == 0) {\n            // A 0-size cross-origin intersection means no-intersection.\n            parent = null;\n            parentRect = null;\n            intersectionRect = null;\n          } else {\n            parentRect = crossOriginRect;\n          }\n        } else {\n          parentRect = rootRect;\n        }\n      } else {\n        // Check if there's a frame that can be navigated to.\n        var frame = getParentNode(parent);\n        var frameRect = frame && getBoundingClientRect(frame);\n        var frameIntersect =\n            frame &&\n            this._computeTargetAndRootIntersection(frame, frameRect, rootRect);\n        if (frameRect && frameIntersect) {\n          parent = frame;\n          parentRect = convertFromParentRect(frameRect, frameIntersect);\n        } else {\n          parent = null;\n          intersectionRect = null;\n        }\n      }\n    } else {\n      // If the element has a non-visible overflow, and it's not the <body>\n      // or <html> element, update the intersection rect.\n      // Note: <body> and <html> cannot be clipped to a rect that's not also\n      // the document rect, so no need to compute a new intersection.\n      var doc = parent.ownerDocument;\n      if (parent != doc.body &&\n          parent != doc.documentElement &&\n          parentComputedStyle.overflow != 'visible') {\n        parentRect = getBoundingClientRect(parent);\n      }\n    }\n\n    // If either of the above conditionals set a new parentRect,\n    // calculate new intersection data.\n    if (parentRect) {\n      intersectionRect = computeRectIntersection(parentRect, intersectionRect);\n    }\n    if (!intersectionRect) break;\n    parent = parent && getParentNode(parent);\n  }\n  return intersectionRect;\n};\n\n\n/**\n * Returns the root rect after being expanded by the rootMargin value.\n * @return {ClientRect} The expanded root rect.\n * @private\n */\nIntersectionObserver.prototype._getRootRect = function() {\n  var rootRect;\n  if (this.root && !isDoc(this.root)) {\n    rootRect = getBoundingClientRect(this.root);\n  } else {\n    // Use <html>/<body> instead of window since scroll bars affect size.\n    var doc = isDoc(this.root) ? this.root : document;\n    var html = doc.documentElement;\n    var body = doc.body;\n    rootRect = {\n      top: 0,\n      left: 0,\n      right: html.clientWidth || body.clientWidth,\n      width: html.clientWidth || body.clientWidth,\n      bottom: html.clientHeight || body.clientHeight,\n      height: html.clientHeight || body.clientHeight\n    };\n  }\n  return this._expandRectByRootMargin(rootRect);\n};\n\n\n/**\n * Accepts a rect and expands it by the rootMargin value.\n * @param {DOMRect|ClientRect} rect The rect object to expand.\n * @return {ClientRect} The expanded rect.\n * @private\n */\nIntersectionObserver.prototype._expandRectByRootMargin = function(rect) {\n  var margins = this._rootMarginValues.map(function(margin, i) {\n    return margin.unit == 'px' ? margin.value :\n        margin.value * (i % 2 ? rect.width : rect.height) / 100;\n  });\n  var newRect = {\n    top: rect.top - margins[0],\n    right: rect.right + margins[1],\n    bottom: rect.bottom + margins[2],\n    left: rect.left - margins[3]\n  };\n  newRect.width = newRect.right - newRect.left;\n  newRect.height = newRect.bottom - newRect.top;\n\n  return newRect;\n};\n\n\n/**\n * Accepts an old and new entry and returns true if at least one of the\n * threshold values has been crossed.\n * @param {?IntersectionObserverEntry} oldEntry The previous entry for a\n *    particular target element or null if no previous entry exists.\n * @param {IntersectionObserverEntry} newEntry The current entry for a\n *    particular target element.\n * @return {boolean} Returns true if a any threshold has been crossed.\n * @private\n */\nIntersectionObserver.prototype._hasCrossedThreshold =\n    function(oldEntry, newEntry) {\n\n  // To make comparing easier, an entry that has a ratio of 0\n  // but does not actually intersect is given a value of -1\n  var oldRatio = oldEntry && oldEntry.isIntersecting ?\n      oldEntry.intersectionRatio || 0 : -1;\n  var newRatio = newEntry.isIntersecting ?\n      newEntry.intersectionRatio || 0 : -1;\n\n  // Ignore unchanged ratios\n  if (oldRatio === newRatio) return;\n\n  for (var i = 0; i < this.thresholds.length; i++) {\n    var threshold = this.thresholds[i];\n\n    // Return true if an entry matches a threshold or if the new ratio\n    // and the old ratio are on the opposite sides of a threshold.\n    if (threshold == oldRatio || threshold == newRatio ||\n        threshold < oldRatio !== threshold < newRatio) {\n      return true;\n    }\n  }\n};\n\n\n/**\n * Returns whether or not the root element is an element and is in the DOM.\n * @return {boolean} True if the root element is an element and is in the DOM.\n * @private\n */\nIntersectionObserver.prototype._rootIsInDom = function() {\n  return !this.root || containsDeep(document, this.root);\n};\n\n\n/**\n * Returns whether or not the target element is a child of root.\n * @param {Element} target The target element to check.\n * @return {boolean} True if the target element is a child of root.\n * @private\n */\nIntersectionObserver.prototype._rootContainsTarget = function(target) {\n  var rootDoc =\n    (this.root && (this.root.ownerDocument || this.root)) || document;\n  return (\n    containsDeep(rootDoc, target) &&\n    (!this.root || rootDoc == target.ownerDocument)\n  );\n};\n\n\n/**\n * Adds the instance to the global IntersectionObserver registry if it isn't\n * already present.\n * @private\n */\nIntersectionObserver.prototype._registerInstance = function() {\n  if (registry.indexOf(this) < 0) {\n    registry.push(this);\n  }\n};\n\n\n/**\n * Removes the instance from the global IntersectionObserver registry.\n * @private\n */\nIntersectionObserver.prototype._unregisterInstance = function() {\n  var index = registry.indexOf(this);\n  if (index != -1) registry.splice(index, 1);\n};\n\n\n/**\n * Returns the result of the performance.now() method or null in browsers\n * that don't support the API.\n * @return {number} The elapsed time since the page was requested.\n */\nfunction now() {\n  return window.performance && performance.now && performance.now();\n}\n\n\n/**\n * Throttles a function and delays its execution, so it's only called at most\n * once within a given time period.\n * @param {Function} fn The function to throttle.\n * @param {number} timeout The amount of time that must pass before the\n *     function can be called again.\n * @return {Function} The throttled function.\n */\nfunction throttle(fn, timeout) {\n  var timer = null;\n  return function () {\n    if (!timer) {\n      timer = setTimeout(function() {\n        fn();\n        timer = null;\n      }, timeout);\n    }\n  };\n}\n\n\n/**\n * Adds an event handler to a DOM node ensuring cross-browser compatibility.\n * @param {Node} node The DOM node to add the event handler to.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to add.\n * @param {boolean} opt_useCapture Optionally adds the even to the capture\n *     phase. Note: this only works in modern browsers.\n */\nfunction addEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.addEventListener == 'function') {\n    node.addEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.attachEvent == 'function') {\n    node.attachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Removes a previously added event handler from a DOM node.\n * @param {Node} node The DOM node to remove the event handler from.\n * @param {string} event The event name.\n * @param {Function} fn The event handler to remove.\n * @param {boolean} opt_useCapture If the event handler was added with this\n *     flag set to true, it should be set to true here in order to remove it.\n */\nfunction removeEvent(node, event, fn, opt_useCapture) {\n  if (typeof node.removeEventListener == 'function') {\n    node.removeEventListener(event, fn, opt_useCapture || false);\n  }\n  else if (typeof node.detachEvent == 'function') {\n    node.detachEvent('on' + event, fn);\n  }\n}\n\n\n/**\n * Returns the intersection between two rect objects.\n * @param {Object} rect1 The first rect.\n * @param {Object} rect2 The second rect.\n * @return {?Object|?ClientRect} The intersection rect or undefined if no\n *     intersection is found.\n */\nfunction computeRectIntersection(rect1, rect2) {\n  var top = Math.max(rect1.top, rect2.top);\n  var bottom = Math.min(rect1.bottom, rect2.bottom);\n  var left = Math.max(rect1.left, rect2.left);\n  var right = Math.min(rect1.right, rect2.right);\n  var width = right - left;\n  var height = bottom - top;\n\n  return (width >= 0 && height >= 0) && {\n    top: top,\n    bottom: bottom,\n    left: left,\n    right: right,\n    width: width,\n    height: height\n  } || null;\n}\n\n\n/**\n * Shims the native getBoundingClientRect for compatibility with older IE.\n * @param {Element} el The element whose bounding rect to get.\n * @return {DOMRect|ClientRect} The (possibly shimmed) rect of the element.\n */\nfunction getBoundingClientRect(el) {\n  var rect;\n\n  try {\n    rect = el.getBoundingClientRect();\n  } catch (err) {\n    // Ignore Windows 7 IE11 \"Unspecified error\"\n    // https://github.com/w3c/IntersectionObserver/pull/205\n  }\n\n  if (!rect) return getEmptyRect();\n\n  // Older IE\n  if (!(rect.width && rect.height)) {\n    rect = {\n      top: rect.top,\n      right: rect.right,\n      bottom: rect.bottom,\n      left: rect.left,\n      width: rect.right - rect.left,\n      height: rect.bottom - rect.top\n    };\n  }\n  return rect;\n}\n\n\n/**\n * Returns an empty rect object. An empty rect is returned when an element\n * is not in the DOM.\n * @return {ClientRect} The empty rect.\n */\nfunction getEmptyRect() {\n  return {\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n    width: 0,\n    height: 0\n  };\n}\n\n\n/**\n * Ensure that the result has all of the necessary fields of the DOMRect.\n * Specifically this ensures that `x` and `y` fields are set.\n *\n * @param {?DOMRect|?ClientRect} rect\n * @return {?DOMRect}\n */\nfunction ensureDOMRect(rect) {\n  // A `DOMRect` object has `x` and `y` fields.\n  if (!rect || 'x' in rect) {\n    return rect;\n  }\n  // A IE's `ClientRect` type does not have `x` and `y`. The same is the case\n  // for internally calculated Rect objects. For the purposes of\n  // `IntersectionObserver`, it's sufficient to simply mirror `left` and `top`\n  // for these fields.\n  return {\n    top: rect.top,\n    y: rect.top,\n    bottom: rect.bottom,\n    left: rect.left,\n    x: rect.left,\n    right: rect.right,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\n\n/**\n * Inverts the intersection and bounding rect from the parent (frame) BCR to\n * the local BCR space.\n * @param {DOMRect|ClientRect} parentBoundingRect The parent's bound client rect.\n * @param {DOMRect|ClientRect} parentIntersectionRect The parent's own intersection rect.\n * @return {ClientRect} The local root bounding rect for the parent's children.\n */\nfunction convertFromParentRect(parentBoundingRect, parentIntersectionRect) {\n  var top = parentIntersectionRect.top - parentBoundingRect.top;\n  var left = parentIntersectionRect.left - parentBoundingRect.left;\n  return {\n    top: top,\n    left: left,\n    height: parentIntersectionRect.height,\n    width: parentIntersectionRect.width,\n    bottom: top + parentIntersectionRect.height,\n    right: left + parentIntersectionRect.width\n  };\n}\n\n\n/**\n * Checks to see if a parent element contains a child element (including inside\n * shadow DOM).\n * @param {Node} parent The parent element.\n * @param {Node} child The child element.\n * @return {boolean} True if the parent node contains the child node.\n */\nfunction containsDeep(parent, child) {\n  var node = child;\n  while (node) {\n    if (node == parent) return true;\n\n    node = getParentNode(node);\n  }\n  return false;\n}\n\n\n/**\n * Gets the parent node of an element or its host element if the parent node\n * is a shadow root.\n * @param {Node} node The node whose parent to get.\n * @return {Node|null} The parent node or null if no parent exists.\n */\nfunction getParentNode(node) {\n  var parent = node.parentNode;\n\n  if (node.nodeType == /* DOCUMENT */ 9 && node != document) {\n    // If this node is a document node, look for the embedding frame.\n    return getFrameElement(node);\n  }\n\n  // If the parent has element that is assigned through shadow root slot\n  if (parent && parent.assignedSlot) {\n    parent = parent.assignedSlot.parentNode\n  }\n\n  if (parent && parent.nodeType == 11 && parent.host) {\n    // If the parent is a shadow root, return the host element.\n    return parent.host;\n  }\n\n  return parent;\n}\n\n/**\n * Returns true if `node` is a Document.\n * @param {!Node} node\n * @returns {boolean}\n */\nfunction isDoc(node) {\n  return node && node.nodeType === 9;\n}\n\n\n// Exposes the constructors globally.\nwindow.IntersectionObserver = IntersectionObserver;\nwindow.IntersectionObserverEntry = IntersectionObserverEntry;\n\n}());\n"], "mappings": ";CAQC,WAAW;AACZ;AAGA,MAAI,OAAO,WAAW,UAAU;AAC9B;AAAA,EACF;AAIA,MAAI,0BAA0B,UAC1B,+BAA+B,UAC/B,uBAAuB,OAAO,0BAA0B,WAAW;AAIrE,QAAI,EAAE,oBAAoB,OAAO,0BAA0B,YAAY;AACrE,aAAO;AAAA,QAAe,OAAO,0BAA0B;AAAA,QACrD;AAAA,QAAkB;AAAA,UAClB,KAAK,WAAY;AACf,mBAAO,KAAK,oBAAoB;AAAA,UAClC;AAAA,QACF;AAAA,MAAC;AAAA,IACH;AACA;AAAA,EACF;AAOA,WAAS,gBAAgB,KAAK;AAC5B,QAAI;AACF,aAAO,IAAI,eAAe,IAAI,YAAY,gBAAgB;AAAA,IAC5D,SAAS,GAAG;AAEV,aAAO;AAAA,IACT;AAAA,EACF;AAKA,MAAI,WAAY,SAAS,UAAU;AACjC,QAAI,MAAM;AACV,QAAI,QAAQ,gBAAgB,GAAG;AAC/B,WAAO,OAAO;AACZ,YAAM,MAAM;AACZ,cAAQ,gBAAgB,GAAG;AAAA,IAC7B;AACA,WAAO;AAAA,EACT,EAAG,OAAO,QAAQ;AAQlB,MAAI,WAAW,CAAC;AAOhB,MAAI,qBAAqB;AAMzB,MAAI,kBAAkB;AAStB,WAAS,0BAA0B,OAAO;AACxC,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,MAAM;AACpB,SAAK,aAAa,cAAc,MAAM,UAAU;AAChD,SAAK,qBAAqB,cAAc,MAAM,kBAAkB;AAChE,SAAK,mBAAmB,cAAc,MAAM,oBAAoB,aAAa,CAAC;AAC9E,SAAK,iBAAiB,CAAC,CAAC,MAAM;AAG9B,QAAI,aAAa,KAAK;AACtB,QAAI,aAAa,WAAW,QAAQ,WAAW;AAC/C,QAAI,mBAAmB,KAAK;AAC5B,QAAI,mBAAmB,iBAAiB,QAAQ,iBAAiB;AAGjE,QAAI,YAAY;AAGd,WAAK,oBAAoB,QAAQ,mBAAmB,YAAY,QAAQ,CAAC,CAAC;AAAA,IAC5E,OAAO;AAEL,WAAK,oBAAoB,KAAK,iBAAiB,IAAI;AAAA,IACrD;AAAA,EACF;AAYA,WAAS,qBAAqB,UAAU,aAAa;AAEnD,QAAI,UAAU,eAAe,CAAC;AAE9B,QAAI,OAAO,YAAY,YAAY;AACjC,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAEA,QACE,QAAQ,QACR,QAAQ,KAAK,YAAY,KACzB,QAAQ,KAAK,YAAY,GACzB;AACA,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACtD;AAGA,SAAK,yBAAyB;AAAA,MAC1B,KAAK,uBAAuB,KAAK,IAAI;AAAA,MAAG,KAAK;AAAA,IAAgB;AAGjE,SAAK,YAAY;AACjB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,KAAK,iBAAiB,QAAQ,UAAU;AAGjE,SAAK,aAAa,KAAK,gBAAgB,QAAQ,SAAS;AACxD,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,aAAa,KAAK,kBAAkB,IAAI,SAAS,QAAQ;AAC5D,aAAO,OAAO,QAAQ,OAAO;AAAA,IAC/B,CAAC,EAAE,KAAK,GAAG;AAGX,SAAK,uBAAuB,CAAC;AAE7B,SAAK,0BAA0B,CAAC;AAAA,EAClC;AAOA,uBAAqB,UAAU,mBAAmB;AAQlD,uBAAqB,UAAU,gBAAgB;AAM/C,uBAAqB,UAAU,wBAAwB;AAYvD,uBAAqB,2BAA2B,WAAW;AACzD,QAAI,CAAC,oBAAoB;AAKvB,2BAAqB,SAAS,oBAAoB,kBAAkB;AAClE,YAAI,CAAC,sBAAsB,CAAC,kBAAkB;AAC5C,4BAAkB,aAAa;AAAA,QACjC,OAAO;AACL,4BAAkB,sBAAsB,oBAAoB,gBAAgB;AAAA,QAC9E;AACA,iBAAS,QAAQ,SAAS,UAAU;AAClC,mBAAS,uBAAuB;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAMA,uBAAqB,2BAA2B,WAAW;AACzD,yBAAqB;AACrB,sBAAkB;AAAA,EACpB;AAQA,uBAAqB,UAAU,UAAU,SAAS,QAAQ;AACxD,QAAI,0BAA0B,KAAK,oBAAoB,KAAK,SAAS,MAAM;AACzE,aAAO,KAAK,WAAW;AAAA,IACzB,CAAC;AAED,QAAI,yBAAyB;AAC3B;AAAA,IACF;AAEA,QAAI,EAAE,UAAU,OAAO,YAAY,IAAI;AACrC,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAEA,SAAK,kBAAkB;AACvB,SAAK,oBAAoB,KAAK,EAAC,SAAS,QAAQ,OAAO,KAAI,CAAC;AAC5D,SAAK,sBAAsB,OAAO,aAAa;AAC/C,SAAK,uBAAuB;AAAA,EAC9B;AAOA,uBAAqB,UAAU,YAAY,SAAS,QAAQ;AAC1D,SAAK,sBACD,KAAK,oBAAoB,OAAO,SAAS,MAAM;AAC7C,aAAO,KAAK,WAAW;AAAA,IACzB,CAAC;AACL,SAAK,wBAAwB,OAAO,aAAa;AACjD,QAAI,KAAK,oBAAoB,UAAU,GAAG;AACxC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAMA,uBAAqB,UAAU,aAAa,WAAW;AACrD,SAAK,sBAAsB,CAAC;AAC5B,SAAK,2BAA2B;AAChC,SAAK,oBAAoB;AAAA,EAC3B;AASA,uBAAqB,UAAU,cAAc,WAAW;AACtD,QAAI,UAAU,KAAK,eAAe,MAAM;AACxC,SAAK,iBAAiB,CAAC;AACvB,WAAO;AAAA,EACT;AAYA,uBAAqB,UAAU,kBAAkB,SAAS,eAAe;AACvE,QAAI,YAAY,iBAAiB,CAAC,CAAC;AACnC,QAAI,CAAC,MAAM,QAAQ,SAAS,EAAG,aAAY,CAAC,SAAS;AAErD,WAAO,UAAU,KAAK,EAAE,OAAO,SAAS,GAAG,GAAG,GAAG;AAC/C,UAAI,OAAO,KAAK,YAAY,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AACtD,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AACA,aAAO,MAAM,EAAE,IAAI,CAAC;AAAA,IACtB,CAAC;AAAA,EACH;AAcA,uBAAqB,UAAU,mBAAmB,SAAS,gBAAgB;AACzE,QAAI,eAAe,kBAAkB;AACrC,QAAI,UAAU,aAAa,MAAM,KAAK,EAAE,IAAI,SAAS,QAAQ;AAC3D,UAAI,QAAQ,wBAAwB,KAAK,MAAM;AAC/C,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AACA,aAAO,EAAC,OAAO,WAAW,MAAM,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,EAAC;AAAA,IACrD,CAAC;AAGD,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACpC,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACpC,YAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAEpC,WAAO;AAAA,EACT;AASA,uBAAqB,UAAU,wBAAwB,SAAS,KAAK;AACnE,QAAI,MAAM,IAAI;AACd,QAAI,CAAC,KAAK;AAER;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB,QAAQ,GAAG,KAAK,IAAI;AAEhD;AAAA,IACF;AAGA,QAAI,WAAW,KAAK;AACpB,QAAI,qBAAqB;AACzB,QAAI,cAAc;AAIlB,QAAI,KAAK,eAAe;AACtB,2BAAqB,IAAI,YAAY,UAAU,KAAK,aAAa;AAAA,IACnE,OAAO;AACL,eAAS,KAAK,UAAU,UAAU,IAAI;AACtC,eAAS,KAAK,UAAU,UAAU,IAAI;AACtC,UAAI,KAAK,yBAAyB,sBAAsB,KAAK;AAC3D,sBAAc,IAAI,IAAI,iBAAiB,QAAQ;AAC/C,oBAAY,QAAQ,KAAK;AAAA,UACvB,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,eAAe;AAAA,UACf,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAEA,SAAK,qBAAqB,KAAK,GAAG;AAClC,SAAK,wBAAwB,KAAK,WAAW;AAG3C,UAAIA,OAAM,IAAI;AAEd,UAAIA,MAAK;AACP,YAAI,oBAAoB;AACtB,UAAAA,KAAI,cAAc,kBAAkB;AAAA,QACtC;AACA,oBAAYA,MAAK,UAAU,UAAU,IAAI;AAAA,MAC3C;AAEA,kBAAY,KAAK,UAAU,UAAU,IAAI;AACzC,UAAI,aAAa;AACf,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF,CAAC;AAGD,QAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAU;AAC3D,QAAI,OAAO,SAAS;AAClB,UAAI,QAAQ,gBAAgB,GAAG;AAC/B,UAAI,OAAO;AACT,aAAK,sBAAsB,MAAM,aAAa;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAQA,uBAAqB,UAAU,0BAA0B,SAAS,KAAK;AACrE,QAAI,QAAQ,KAAK,qBAAqB,QAAQ,GAAG;AACjD,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AAEA,QAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAU;AAG3D,QAAI,sBACA,KAAK,oBAAoB,KAAK,SAAS,MAAM;AAC3C,UAAI,UAAU,KAAK,QAAQ;AAE3B,UAAI,WAAW,KAAK;AAClB,eAAO;AAAA,MACT;AAEA,aAAO,WAAW,WAAW,SAAS;AACpC,YAAIC,SAAQ,gBAAgB,OAAO;AACnC,kBAAUA,UAASA,OAAM;AACzB,YAAI,WAAW,KAAK;AAClB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC;AACL,QAAI,qBAAqB;AACvB;AAAA,IACF;AAGA,QAAI,cAAc,KAAK,wBAAwB,KAAK;AACpD,SAAK,qBAAqB,OAAO,OAAO,CAAC;AACzC,SAAK,wBAAwB,OAAO,OAAO,CAAC;AAC5C,gBAAY;AAGZ,QAAI,OAAO,SAAS;AAClB,UAAI,QAAQ,gBAAgB,GAAG;AAC/B,UAAI,OAAO;AACT,aAAK,wBAAwB,MAAM,aAAa;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAQA,uBAAqB,UAAU,6BAA6B,WAAW;AACrE,QAAI,eAAe,KAAK,wBAAwB,MAAM,CAAC;AACvD,SAAK,qBAAqB,SAAS;AACnC,SAAK,wBAAwB,SAAS;AACtC,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,mBAAa,CAAC,EAAE;AAAA,IAClB;AAAA,EACF;AASA,uBAAqB,UAAU,yBAAyB,WAAW;AACjE,QAAI,CAAC,KAAK,QAAQ,sBAAsB,CAAC,iBAAiB;AAExD;AAAA,IACF;AAEA,QAAI,cAAc,KAAK,aAAa;AACpC,QAAI,WAAW,cAAc,KAAK,aAAa,IAAI,aAAa;AAEhE,SAAK,oBAAoB,QAAQ,SAAS,MAAM;AAC9C,UAAI,SAAS,KAAK;AAClB,UAAI,aAAa,sBAAsB,MAAM;AAC7C,UAAI,qBAAqB,KAAK,oBAAoB,MAAM;AACxD,UAAI,WAAW,KAAK;AACpB,UAAI,mBAAmB,eAAe,sBAClC,KAAK,kCAAkC,QAAQ,YAAY,QAAQ;AAEvE,UAAI,aAAa;AACjB,UAAI,CAAC,KAAK,oBAAoB,MAAM,GAAG;AACrC,qBAAa,aAAa;AAAA,MAC5B,WAAW,CAAC,sBAAsB,KAAK,MAAM;AAC3C,qBAAa;AAAA,MACf;AAEA,UAAI,WAAW,KAAK,QAAQ,IAAI,0BAA0B;AAAA,QACxD,MAAM,IAAI;AAAA,QACV;AAAA,QACA,oBAAoB;AAAA,QACpB;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI,CAAC,UAAU;AACb,aAAK,eAAe,KAAK,QAAQ;AAAA,MACnC,WAAW,eAAe,oBAAoB;AAG5C,YAAI,KAAK,qBAAqB,UAAU,QAAQ,GAAG;AACjD,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF,OAAO;AAIL,YAAI,YAAY,SAAS,gBAAgB;AACvC,eAAK,eAAe,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AAAA,IACF,GAAG,IAAI;AAEP,QAAI,KAAK,eAAe,QAAQ;AAC9B,WAAK,UAAU,KAAK,YAAY,GAAG,IAAI;AAAA,IACzC;AAAA,EACF;AAgBA,uBAAqB,UAAU,oCAC3B,SAAS,QAAQ,YAAY,UAAU;AAEzC,QAAI,OAAO,iBAAiB,MAAM,EAAE,WAAW,OAAQ;AAEvD,QAAI,mBAAmB;AACvB,QAAI,SAAS,cAAc,MAAM;AACjC,QAAI,SAAS;AAEb,WAAO,CAAC,UAAU,QAAQ;AACxB,UAAI,aAAa;AACjB,UAAI,sBAAsB,OAAO,YAAY,IACzC,OAAO,iBAAiB,MAAM,IAAI,CAAC;AAGvC,UAAI,oBAAoB,WAAW,OAAQ,QAAO;AAElD,UAAI,UAAU,KAAK,QAAQ,OAAO;AAAA,MAA2B,GAAG;AAC9D,iBAAS;AACT,YAAI,UAAU,KAAK,QAAQ,UAAU,UAAU;AAC7C,cAAI,sBAAsB,CAAC,KAAK,MAAM;AACpC,gBAAI,CAAC,mBACD,gBAAgB,SAAS,KAAK,gBAAgB,UAAU,GAAG;AAE7D,uBAAS;AACT,2BAAa;AACb,iCAAmB;AAAA,YACrB,OAAO;AACL,2BAAa;AAAA,YACf;AAAA,UACF,OAAO;AACL,yBAAa;AAAA,UACf;AAAA,QACF,OAAO;AAEL,cAAI,QAAQ,cAAc,MAAM;AAChC,cAAI,YAAY,SAAS,sBAAsB,KAAK;AACpD,cAAI,iBACA,SACA,KAAK,kCAAkC,OAAO,WAAW,QAAQ;AACrE,cAAI,aAAa,gBAAgB;AAC/B,qBAAS;AACT,yBAAa,sBAAsB,WAAW,cAAc;AAAA,UAC9D,OAAO;AACL,qBAAS;AACT,+BAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF,OAAO;AAKL,YAAI,MAAM,OAAO;AACjB,YAAI,UAAU,IAAI,QACd,UAAU,IAAI,mBACd,oBAAoB,YAAY,WAAW;AAC7C,uBAAa,sBAAsB,MAAM;AAAA,QAC3C;AAAA,MACF;AAIA,UAAI,YAAY;AACd,2BAAmB,wBAAwB,YAAY,gBAAgB;AAAA,MACzE;AACA,UAAI,CAAC,iBAAkB;AACvB,eAAS,UAAU,cAAc,MAAM;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAQA,uBAAqB,UAAU,eAAe,WAAW;AACvD,QAAI;AACJ,QAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,GAAG;AAClC,iBAAW,sBAAsB,KAAK,IAAI;AAAA,IAC5C,OAAO;AAEL,UAAI,MAAM,MAAM,KAAK,IAAI,IAAI,KAAK,OAAO;AACzC,UAAI,OAAO,IAAI;AACf,UAAI,OAAO,IAAI;AACf,iBAAW;AAAA,QACT,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO,KAAK,eAAe,KAAK;AAAA,QAChC,OAAO,KAAK,eAAe,KAAK;AAAA,QAChC,QAAQ,KAAK,gBAAgB,KAAK;AAAA,QAClC,QAAQ,KAAK,gBAAgB,KAAK;AAAA,MACpC;AAAA,IACF;AACA,WAAO,KAAK,wBAAwB,QAAQ;AAAA,EAC9C;AASA,uBAAqB,UAAU,0BAA0B,SAAS,MAAM;AACtE,QAAI,UAAU,KAAK,kBAAkB,IAAI,SAAS,QAAQ,GAAG;AAC3D,aAAO,OAAO,QAAQ,OAAO,OAAO,QAChC,OAAO,SAAS,IAAI,IAAI,KAAK,QAAQ,KAAK,UAAU;AAAA,IAC1D,CAAC;AACD,QAAI,UAAU;AAAA,MACZ,KAAK,KAAK,MAAM,QAAQ,CAAC;AAAA,MACzB,OAAO,KAAK,QAAQ,QAAQ,CAAC;AAAA,MAC7B,QAAQ,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC/B,MAAM,KAAK,OAAO,QAAQ,CAAC;AAAA,IAC7B;AACA,YAAQ,QAAQ,QAAQ,QAAQ,QAAQ;AACxC,YAAQ,SAAS,QAAQ,SAAS,QAAQ;AAE1C,WAAO;AAAA,EACT;AAaA,uBAAqB,UAAU,uBAC3B,SAAS,UAAU,UAAU;AAI/B,QAAI,WAAW,YAAY,SAAS,iBAChC,SAAS,qBAAqB,IAAI;AACtC,QAAI,WAAW,SAAS,iBACpB,SAAS,qBAAqB,IAAI;AAGtC,QAAI,aAAa,SAAU;AAE3B,aAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,UAAI,YAAY,KAAK,WAAW,CAAC;AAIjC,UAAI,aAAa,YAAY,aAAa,YACtC,YAAY,aAAa,YAAY,UAAU;AACjD,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAQA,uBAAqB,UAAU,eAAe,WAAW;AACvD,WAAO,CAAC,KAAK,QAAQ,aAAa,UAAU,KAAK,IAAI;AAAA,EACvD;AASA,uBAAqB,UAAU,sBAAsB,SAAS,QAAQ;AACpE,QAAI,UACD,KAAK,SAAS,KAAK,KAAK,iBAAiB,KAAK,SAAU;AAC3D,WACE,aAAa,SAAS,MAAM,MAC3B,CAAC,KAAK,QAAQ,WAAW,OAAO;AAAA,EAErC;AAQA,uBAAqB,UAAU,oBAAoB,WAAW;AAC5D,QAAI,SAAS,QAAQ,IAAI,IAAI,GAAG;AAC9B,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAOA,uBAAqB,UAAU,sBAAsB,WAAW;AAC9D,QAAI,QAAQ,SAAS,QAAQ,IAAI;AACjC,QAAI,SAAS,GAAI,UAAS,OAAO,OAAO,CAAC;AAAA,EAC3C;AAQA,WAAS,MAAM;AACb,WAAO,OAAO,eAAe,YAAY,OAAO,YAAY,IAAI;AAAA,EAClE;AAWA,WAAS,SAAS,IAAI,SAAS;AAC7B,QAAI,QAAQ;AACZ,WAAO,WAAY;AACjB,UAAI,CAAC,OAAO;AACV,gBAAQ,WAAW,WAAW;AAC5B,aAAG;AACH,kBAAQ;AAAA,QACV,GAAG,OAAO;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAWA,WAAS,SAAS,MAAM,OAAO,IAAI,gBAAgB;AACjD,QAAI,OAAO,KAAK,oBAAoB,YAAY;AAC9C,WAAK,iBAAiB,OAAO,IAAI,kBAAkB,KAAK;AAAA,IAC1D,WACS,OAAO,KAAK,eAAe,YAAY;AAC9C,WAAK,YAAY,OAAO,OAAO,EAAE;AAAA,IACnC;AAAA,EACF;AAWA,WAAS,YAAY,MAAM,OAAO,IAAI,gBAAgB;AACpD,QAAI,OAAO,KAAK,uBAAuB,YAAY;AACjD,WAAK,oBAAoB,OAAO,IAAI,kBAAkB,KAAK;AAAA,IAC7D,WACS,OAAO,KAAK,eAAe,YAAY;AAC9C,WAAK,YAAY,OAAO,OAAO,EAAE;AAAA,IACnC;AAAA,EACF;AAUA,WAAS,wBAAwB,OAAO,OAAO;AAC7C,QAAI,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,GAAG;AACvC,QAAI,SAAS,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAChD,QAAI,OAAO,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI;AAC1C,QAAI,QAAQ,KAAK,IAAI,MAAM,OAAO,MAAM,KAAK;AAC7C,QAAI,QAAQ,QAAQ;AACpB,QAAI,SAAS,SAAS;AAEtB,WAAQ,SAAS,KAAK,UAAU,KAAM;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,KAAK;AAAA,EACP;AAQA,WAAS,sBAAsB,IAAI;AACjC,QAAI;AAEJ,QAAI;AACF,aAAO,GAAG,sBAAsB;AAAA,IAClC,SAAS,KAAK;AAAA,IAGd;AAEA,QAAI,CAAC,KAAM,QAAO,aAAa;AAG/B,QAAI,EAAE,KAAK,SAAS,KAAK,SAAS;AAChC,aAAO;AAAA,QACL,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,QAAQ,KAAK;AAAA,QACzB,QAAQ,KAAK,SAAS,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAQA,WAAS,eAAe;AACtB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AAUA,WAAS,cAAc,MAAM;AAE3B,QAAI,CAAC,QAAQ,OAAO,MAAM;AACxB,aAAO;AAAA,IACT;AAKA,WAAO;AAAA,MACL,KAAK,KAAK;AAAA,MACV,GAAG,KAAK;AAAA,MACR,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,GAAG,KAAK;AAAA,MACR,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AAUA,WAAS,sBAAsB,oBAAoB,wBAAwB;AACzE,QAAI,MAAM,uBAAuB,MAAM,mBAAmB;AAC1D,QAAI,OAAO,uBAAuB,OAAO,mBAAmB;AAC5D,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,QAAQ,uBAAuB;AAAA,MAC/B,OAAO,uBAAuB;AAAA,MAC9B,QAAQ,MAAM,uBAAuB;AAAA,MACrC,OAAO,OAAO,uBAAuB;AAAA,IACvC;AAAA,EACF;AAUA,WAAS,aAAa,QAAQ,OAAO;AACnC,QAAI,OAAO;AACX,WAAO,MAAM;AACX,UAAI,QAAQ,OAAQ,QAAO;AAE3B,aAAO,cAAc,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AASA,WAAS,cAAc,MAAM;AAC3B,QAAI,SAAS,KAAK;AAElB,QAAI,KAAK;AAAA,IAA2B,KAAK,QAAQ,UAAU;AAEzD,aAAO,gBAAgB,IAAI;AAAA,IAC7B;AAGA,QAAI,UAAU,OAAO,cAAc;AACjC,eAAS,OAAO,aAAa;AAAA,IAC/B;AAEA,QAAI,UAAU,OAAO,YAAY,MAAM,OAAO,MAAM;AAElD,aAAO,OAAO;AAAA,IAChB;AAEA,WAAO;AAAA,EACT;AAOA,WAAS,MAAM,MAAM;AACnB,WAAO,QAAQ,KAAK,aAAa;AAAA,EACnC;AAIA,SAAO,uBAAuB;AAC9B,SAAO,4BAA4B;AAEnC,GAAE;", "names": ["win", "frame"]}